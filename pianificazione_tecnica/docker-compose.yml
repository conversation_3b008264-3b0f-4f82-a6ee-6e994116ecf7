version: '3.8'

services:
  # SQL Server Database
  sqlserver:
    image: mcr.microsoft.com/mssql/server:2022-latest
    container_name: budget-sqlserver
    environment:
      - ACCEPT_EULA=Y
      - SA_PASSWORD=BudgetManagement123!
      - MSSQL_PID=Developer
    ports:
      - "1433:1433"
    volumes:
      - sqlserver_data:/var/opt/mssql
    networks:
      - budget-network

  # Redis Cache
  redis:
    image: redis:7-alpine
    container_name: budget-redis
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    networks:
      - budget-network

  # Web API
  webapi:
    build:
      context: .
      dockerfile: src/BudgetManagement.WebAPI/Dockerfile
    container_name: budget-webapi
    environment:
      - ASPNETCORE_ENVIRONMENT=Development
      - ASPNETCORE_URLS=http://+:80
      - ConnectionStrings__DefaultConnection=Server=sqlserver;Database=BudgetManagement;User Id=sa;Password=BudgetManagement123!;TrustServerCertificate=true
      - ConnectionStrings__Redis=redis:6379
    ports:
      - "5000:80"
    depends_on:
      - sqlserver
      - redis
    networks:
      - budget-network

  # Frontend (React)
  webapp:
    build:
      context: .
      dockerfile: src/BudgetManagement.WebApp/Dockerfile
    container_name: budget-webapp
    environment:
      - REACT_APP_API_URL=http://localhost:5000
    ports:
      - "3000:80"
    depends_on:
      - webapi
    networks:
      - budget-network

volumes:
  sqlserver_data:
  redis_data:

networks:
  budget-network:
    driver: bridge
