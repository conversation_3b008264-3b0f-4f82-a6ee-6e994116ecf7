import React from 'react';
import { Card, Row, Col, Statistic, Typography, Space, Button } from 'antd';
import { 
  CalculatorOutlined, 
  CheckCircleOutlined, 
  ClockCircleOutlined,
  ExclamationCircleOutlined,
  PlusOutlined,
  BarChartOutlined
} from '@ant-design/icons';
import { Link } from 'react-router-dom';

const { Title, Paragraph } = Typography;

export const Dashboard: React.FC = () => {
  // Mock data - in real app this would come from API
  const stats = {
    totalBudgets: 24,
    approvedBudgets: 18,
    pendingBudgets: 4,
    rejectedBudgets: 2,
  };

  return (
    <div style={{ padding: '24px', background: '#fff', borderRadius: '8px' }}>
      <div style={{ marginBottom: 32 }}>
        <Title level={2}>Dashboard Budget Management</Title>
        <Paragraph>
          Panoramica generale del sistema di gestione budget annuale per impianti.
        </Paragraph>
      </div>

      {/* Statistics Cards */}
      <Row gutter={[16, 16]} style={{ marginBottom: 32 }}>
        <Col xs={24} sm={12} lg={6}>
          <Card>
            <Statistic
              title="Budget Totali"
              value={stats.totalBudgets}
              prefix={<CalculatorOutlined />}
              valueStyle={{ color: '#1890ff' }}
            />
          </Card>
        </Col>
        <Col xs={24} sm={12} lg={6}>
          <Card>
            <Statistic
              title="Approvati"
              value={stats.approvedBudgets}
              prefix={<CheckCircleOutlined />}
              valueStyle={{ color: '#52c41a' }}
            />
          </Card>
        </Col>
        <Col xs={24} sm={12} lg={6}>
          <Card>
            <Statistic
              title="In Attesa"
              value={stats.pendingBudgets}
              prefix={<ClockCircleOutlined />}
              valueStyle={{ color: '#faad14' }}
            />
          </Card>
        </Col>
        <Col xs={24} sm={12} lg={6}>
          <Card>
            <Statistic
              title="Rifiutati"
              value={stats.rejectedBudgets}
              prefix={<ExclamationCircleOutlined />}
              valueStyle={{ color: '#ff4d4f' }}
            />
          </Card>
        </Col>
      </Row>

      {/* Quick Actions */}
      <Row gutter={[16, 16]}>
        <Col xs={24} lg={12}>
          <Card title="Azioni Rapide" extra={<CalculatorOutlined />}>
            <Space direction="vertical" size="large" style={{ width: '100%' }}>
              <Button 
                type="primary" 
                size="large" 
                icon={<PlusOutlined />}
                block
              >
                <Link to="/budget/create">Crea Nuovo Budget</Link>
              </Button>
              
              <Button 
                size="large" 
                icon={<BarChartOutlined />}
                block
              >
                <Link to="/budget/list">Visualizza Budget Esistenti</Link>
              </Button>
              
              <Button 
                size="large" 
                icon={<BarChartOutlined />}
                block
              >
                <Link to="/budget/reports">Genera Report</Link>
              </Button>
            </Space>
          </Card>
        </Col>

        <Col xs={24} lg={12}>
          <Card title="Attività Recenti" extra={<ClockCircleOutlined />}>
            <Space direction="vertical" size="middle" style={{ width: '100%' }}>
              <div style={{ padding: '8px 0', borderBottom: '1px solid #f0f0f0' }}>
                <div style={{ fontWeight: 500 }}>Budget 2024 - Centrale Termica A</div>
                <div style={{ fontSize: '12px', color: '#666' }}>
                  Creato 2 ore fa
                </div>
              </div>
              
              <div style={{ padding: '8px 0', borderBottom: '1px solid #f0f0f0' }}>
                <div style={{ fontWeight: 500 }}>Budget 2024 - Impianto Idroelettrico B</div>
                <div style={{ fontSize: '12px', color: '#666' }}>
                  Approvato 1 giorno fa
                </div>
              </div>
              
              <div style={{ padding: '8px 0', borderBottom: '1px solid #f0f0f0' }}>
                <div style={{ fontWeight: 500 }}>Budget 2024 - Centrale Termica C</div>
                <div style={{ fontSize: '12px', color: '#666' }}>
                  In revisione da 3 giorni
                </div>
              </div>
              
              <div style={{ padding: '8px 0' }}>
                <div style={{ fontWeight: 500 }}>Budget 2023 - Impianto Idroelettrico D</div>
                <div style={{ fontSize: '12px', color: '#666' }}>
                  Completato 1 settimana fa
                </div>
              </div>
            </Space>
          </Card>
        </Col>
      </Row>
    </div>
  );
};
