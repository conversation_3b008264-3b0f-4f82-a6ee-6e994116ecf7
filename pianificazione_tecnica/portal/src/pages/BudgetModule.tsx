import React, { Suspense } from 'react';
import { Spin, Alert } from 'antd';
import { Routes, Route } from 'react-router-dom';

// Lazy load micro-frontend components
const BudgetApp = React.lazy(() => 
  import('budget-fe/BudgetApp').catch(() => ({
    default: () => (
      <Alert
        message="Micro-frontend non disponibile"
        description="Il modulo Budget non è attualmente disponibile. Verifica che il servizio sia in esecuzione."
        type="warning"
        showIcon
      />
    )
  }))
);

const CreateBudgetForm = React.lazy(() => 
  import('budget-fe/CreateBudgetForm').catch(() => ({
    default: () => (
      <Alert
        message="Componente non disponibile"
        description="Il form di creazione budget non è attualmente disponibile."
        type="warning"
        showIcon
      />
    )
  }))
);

const BudgetList = React.lazy(() => 
  import('budget-fe/BudgetList').catch(() => ({
    default: () => (
      <Alert
        message="Componente non disponibile"
        description="La lista budget non è attualmente disponibile."
        type="warning"
        showIcon
      />
    )
  }))
);

const LoadingSpinner = () => (
  <div style={{ 
    display: 'flex', 
    justifyContent: 'center', 
    alignItems: 'center', 
    height: '200px' 
  }}>
    <Spin size="large" />
  </div>
);

export const BudgetModule: React.FC = () => {
  return (
    <Suspense fallback={<LoadingSpinner />}>
      <Routes>
        <Route path="/" element={<BudgetApp />} />
        <Route path="/create" element={<CreateBudgetForm />} />
        <Route path="/list" element={<BudgetList />} />
        <Route path="/reports" element={
          <div style={{ padding: '24px', background: '#fff', borderRadius: '8px' }}>
            <h2>Report Budget</h2>
            <p>Sezione report in sviluppo...</p>
          </div>
        } />
      </Routes>
    </Suspense>
  );
};
