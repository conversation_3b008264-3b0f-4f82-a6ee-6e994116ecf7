import axios, { AxiosInstance } from 'axios';
import { LoginRequest, LoginResponse, User } from '../types/auth';

// Create axios instance for auth API
const createAuthApiInstance = (): AxiosInstance => {
  const instance = axios.create({
    baseURL: import.meta.env.VITE_AUTH_API_URL || 'http://localhost:5001',
    timeout: 30000,
    headers: {
      'Content-Type': 'application/json',
    },
  });

  // Request interceptor to add auth token
  instance.interceptors.request.use(
    (config) => {
      const token = localStorage.getItem('authToken');
      if (token) {
        config.headers.Authorization = `Bearer ${token}`;
      }
      return config;
    },
    (error) => {
      return Promise.reject(error);
    }
  );

  return instance;
};

const authApiClient = createAuthApiInstance();

export const authApi = {
  // Authentication
  login: async (credentials: LoginRequest): Promise<LoginResponse> => {
    const response = await authApiClient.post('/api/v1/auth/login', credentials);
    return response.data;
  },

  logout: async (): Promise<void> => {
    await authApiClient.post('/api/v1/auth/logout');
  },

  refreshToken: async (): Promise<LoginResponse> => {
    const response = await authApiClient.post('/api/v1/auth/refresh');
    return response.data;
  },

  // User management
  getCurrentUser: async (): Promise<User> => {
    const response = await authApiClient.get('/api/v1/auth/me');
    return response.data;
  },

  // Azure AD integration (if enabled)
  loginWithAzureAd: async (accessToken: string): Promise<LoginResponse> => {
    const response = await authApiClient.post('/api/v1/auth/azure-ad', {
      accessToken,
    });
    return response.data;
  },

  // Health check
  getHealth: async (): Promise<{ status: string; timestamp: string }> => {
    const response = await authApiClient.get('/api/v1/auth/health');
    return response.data;
  },
};
