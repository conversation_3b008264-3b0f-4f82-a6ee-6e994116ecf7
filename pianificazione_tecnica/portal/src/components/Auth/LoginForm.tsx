import React from 'react';
import { Form, Input, But<PERSON>, Card, Typo<PERSON>, Alert, Space } from 'antd';
import { UserOutlined, LockOutlined, MicrosoftOutlined } from '@ant-design/icons';
import { useAuth } from '../../contexts/AuthContext';
import { LoginRequest } from '../../types/auth';

const { Title, Text } = Typography;

export const LoginForm: React.FC = () => {
  const { login, isLoading, error } = useAuth();
  const [form] = Form.useForm();

  const handleSubmit = async (values: LoginRequest) => {
    try {
      await login(values);
    } catch (error) {
      // Error is handled by the auth context
    }
  };

  const handleAzureAdLogin = () => {
    // TODO: Implement Azure AD login
    console.log('Azure AD login not implemented yet');
  };

  return (
    <div style={{
      minHeight: '100vh',
      display: 'flex',
      alignItems: 'center',
      justifyContent: 'center',
      background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
      padding: '20px'
    }}>
      <Card 
        style={{ 
          width: '100%', 
          maxWidth: 400,
          boxShadow: '0 10px 25px rgba(0,0,0,0.1)'
        }}
      >
        <div style={{ textAlign: 'center', marginBottom: 32 }}>
          <Title level={2} style={{ color: '#1890ff', marginBottom: 8 }}>
            Budget Management
          </Title>
          <Text type="secondary">
            Accedi al sistema di gestione budget
          </Text>
        </div>

        {error && (
          <Alert
            message="Errore di accesso"
            description={error}
            type="error"
            showIcon
            style={{ marginBottom: 24 }}
          />
        )}

        <Form
          form={form}
          name="login"
          onFinish={handleSubmit}
          layout="vertical"
          size="large"
        >
          <Form.Item
            name="email"
            label="Email"
            rules={[
              { required: true, message: 'Inserisci la tua email' },
              { type: 'email', message: 'Inserisci un indirizzo email valido' }
            ]}
          >
            <Input 
              prefix={<UserOutlined />} 
              placeholder="<EMAIL>"
            />
          </Form.Item>

          <Form.Item
            name="password"
            label="Password"
            rules={[
              { required: true, message: 'Inserisci la tua password' },
              { min: 6, message: 'La password deve essere di almeno 6 caratteri' }
            ]}
          >
            <Input.Password 
              prefix={<LockOutlined />} 
              placeholder="Password"
            />
          </Form.Item>

          <Form.Item>
            <Button 
              type="primary" 
              htmlType="submit" 
              loading={isLoading}
              block
              style={{ height: 48 }}
            >
              Accedi
            </Button>
          </Form.Item>
        </Form>

        <div style={{ textAlign: 'center' }}>
          <Text type="secondary" style={{ marginBottom: 16, display: 'block' }}>
            oppure
          </Text>
          
          <Button
            icon={<MicrosoftOutlined />}
            onClick={handleAzureAdLogin}
            block
            style={{ height: 48 }}
          >
            Accedi con Microsoft
          </Button>
        </div>

        <div style={{ textAlign: 'center', marginTop: 24 }}>
          <Space direction="vertical" size="small">
            <Text type="secondary" style={{ fontSize: 12 }}>
              Demo Credentials:
            </Text>
            <Text type="secondary" style={{ fontSize: 12 }}>
              Email: <EMAIL>
            </Text>
            <Text type="secondary" style={{ fontSize: 12 }}>
              Password: password123
            </Text>
          </Space>
        </div>
      </Card>
    </div>
  );
};
