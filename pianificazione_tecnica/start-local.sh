#!/bin/bash

# Budget Management System - Local Development Startup Script

set -e

echo "🚀 Starting Budget Management System..."

# Check if Dock<PERSON> is running
if ! docker info > /dev/null 2>&1; then
    echo "❌ Docker is not running. Please start Docker and try again."
    exit 1
fi

# Check if config.env exists, if not copy from template
if [ ! -f "config.env" ]; then
    echo "📋 Creating config.env from template..."
    cp config.env.template config.env
    echo "⚠️  Please review and update config.env with your settings"
fi

# Load environment variables
if [ -f "config.env" ]; then
    export $(cat config.env | grep -v '^#' | xargs)
fi

echo "🐳 Starting infrastructure services..."

# Start PostgreSQL and Redis
docker-compose up -d postgres redis

echo "⏳ Waiting for PostgreSQL to be ready..."
until docker-compose exec -T postgres pg_isready -U postgres; do
    sleep 2
done

echo "✅ PostgreSQL is ready"

echo "🔧 Building and starting Budget API..."
docker-compose up -d budget-api

echo "⏳ Waiting for Budget API to be ready..."
until curl -f http://localhost:5000/api/v1/budget/health > /dev/null 2>&1; do
    sleep 2
done

echo "✅ Budget API is ready"

echo "🎨 Building and starting Frontend services..."
docker-compose up -d budget-fe

echo "⏳ Waiting for Budget Frontend to be ready..."
until curl -f http://localhost:3001/health > /dev/null 2>&1; do
    sleep 2
done

echo "✅ Budget Frontend is ready"

echo "🌐 Starting Portal (Shell Application)..."
docker-compose up -d portal

echo "⏳ Waiting for Portal to be ready..."
until curl -f http://localhost:3000/health > /dev/null 2>&1; do
    sleep 2
done

echo "✅ Portal is ready"

echo ""
echo "🎉 Budget Management System is now running!"
echo ""
echo "📱 Access Points:"
echo "   Portal (Shell):     http://localhost:3000"
echo "   Budget Frontend:    http://localhost:3001"
echo "   Budget API:         http://localhost:5000"
echo "   API Documentation:  http://localhost:5000/swagger"
echo ""
echo "🗄️  Database:"
echo "   PostgreSQL:         localhost:5432"
echo "   Database:           budget_management"
echo "   Username:           postgres"
echo ""
echo "📊 Monitoring:"
echo "   Docker Logs:        docker-compose logs -f"
echo "   API Health:         curl http://localhost:5000/api/v1/budget/health"
echo ""
echo "🛑 To stop all services:"
echo "   docker-compose down"
echo ""

# Show running containers
echo "🐳 Running containers:"
docker-compose ps
