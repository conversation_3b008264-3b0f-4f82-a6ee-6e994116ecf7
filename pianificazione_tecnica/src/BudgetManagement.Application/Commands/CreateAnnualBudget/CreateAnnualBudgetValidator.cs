using FluentValidation;

namespace BudgetManagement.Application.Commands.CreateAnnualBudget;

/// <summary>
/// Validator for the create annual budget command
/// </summary>
public class CreateAnnualBudgetValidator : AbstractValidator<CreateAnnualBudgetCommand>
{
    public CreateAnnualBudgetValidator()
    {
        RuleFor(x => x.PlantId)
            .NotEmpty()
            .WithMessage("Plant ID is required");

        RuleFor(x => x.ProductionUnitId)
            .NotEmpty()
            .WithMessage("Production Unit ID is required");

        RuleFor(x => x.Year)
            .GreaterThanOrEqualTo(2020)
            .LessThanOrEqualTo(2050)
            .WithMessage("Year must be between 2020 and 2050");
    }
}
