using BudgetManagement.Application.Common.Interfaces;
using BudgetManagement.Domain.Common;
using BudgetManagement.Domain.Entities;
using BudgetManagement.Domain.Repositories;
using BudgetManagement.Domain.Services;
using BudgetManagement.Domain.ValueObjects;
using MediatR;
using Microsoft.Extensions.Logging;

namespace BudgetManagement.Application.Commands.CreateAnnualBudget;

/// <summary>
/// Handler for the create annual budget command
/// </summary>
public class CreateAnnualBudgetHandler : IRequestHandler<CreateAnnualBudgetCommand, Result<CreateAnnualBudgetResponse>>
{
    private readonly IBudgetRepository _budgetRepository;
    private readonly IBudgetDomainService _budgetDomainService;
    private readonly IUnitOfWork _unitOfWork;
    private readonly ICurrentUserService _currentUserService;
    private readonly ILogger<CreateAnnualBudgetHandler> _logger;

    public CreateAnnualBudgetHandler(
        IBudgetRepository budgetRepository,
        IBudgetDomainService budgetDomainService,
        IUnitOfWork unitOfWork,
        ICurrentUserService currentUserService,
        ILogger<CreateAnnualBudgetHandler> logger)
    {
        _budgetRepository = budgetRepository;
        _budgetDomainService = budgetDomainService;
        _unitOfWork = unitOfWork;
        _currentUserService = currentUserService;
        _logger = logger;
    }

    public async Task<Result<CreateAnnualBudgetResponse>> Handle(
        CreateAnnualBudgetCommand request,
        CancellationToken cancellationToken)
    {
        try
        {
            // Get current user
            var currentUserId = _currentUserService.UserId;
            if (currentUserId == null)
                return Result.Failure<CreateAnnualBudgetResponse>("User not authenticated");

            var plantId = PlantId.From(request.PlantId);
            var productionUnitId = ProductionUnitId.From(request.ProductionUnitId);
            var year = new Year(request.Year);

            // Validate production unit belongs to plant
            if (!await _budgetDomainService.IsProductionUnitValidForPlantAsync(productionUnitId, plantId))
                return Result.Failure<CreateAnnualBudgetResponse>("Production unit does not belong to the specified plant");

            // Check if production unit is active for the year
            if (!await _budgetDomainService.IsProductionUnitActiveAsync(productionUnitId, year))
                return Result.Failure<CreateAnnualBudgetResponse>("Production unit is not active for the specified year");

            // Check if budget already exists
            if (await _budgetRepository.ExistsAsync(plantId, productionUnitId, year, cancellationToken))
                return Result.Failure<CreateAnnualBudgetResponse>("Budget already exists for this plant, production unit and year");

            // Create the budget
            var budget = AnnualBudget.Create(plantId, productionUnitId, year, currentUserId);

            // Try to find and associate maintenance plan
            string? maintenancePlanInfo = null;
            var maintenancePlanResult = await _budgetDomainService.FindReferencePlanAsync(plantId, year);
            if (maintenancePlanResult.IsSuccess)
            {
                var setMaintenancePlanResult = budget.SetMaintenancePlan(maintenancePlanResult.Value);
                if (setMaintenancePlanResult.IsSuccess)
                {
                    maintenancePlanInfo = $"Associated with maintenance plan {maintenancePlanResult.Value}";
                }
            }

            // Save the budget
            await _budgetRepository.AddAsync(budget, cancellationToken);
            await _unitOfWork.SaveChangesAsync(cancellationToken);

            _logger.LogInformation(
                "Annual budget created successfully. BudgetId: {BudgetId}, PlantId: {PlantId}, Year: {Year}",
                budget.Id, plantId, year);

            return Result.Success(new CreateAnnualBudgetResponse(
                budget.Id,
                budget.Status.ToString(),
                maintenancePlanInfo,
                "Budget created successfully"));
        }
        catch (Exception ex)
        {
            _logger.LogError(ex,
                "Error creating annual budget for PlantId: {PlantId}, ProductionUnitId: {ProductionUnitId}, Year: {Year}",
                request.PlantId, request.ProductionUnitId, request.Year);

            return Result.Failure<CreateAnnualBudgetResponse>("An error occurred while creating the budget");
        }
    }
}
