namespace BudgetManagement.Application.Queries.GetBudget;

/// <summary>
/// Response for the get budget query
/// </summary>
public record GetBudgetResponse(
    Guid Id,
    Guid PlantId,
    Guid ProductionUnitId,
    int Year,
    string Status,
    int Version,
    Guid CreatedBy,
    DateTime CreatedAt,
    DateTime? ModifiedAt,
    Guid? MaintenancePlanId,
    DateTime? ApprovedAtPlantDate,
    Guid? ApprovedAtPlantBy,
    DateTime? ApprovedAtHeadquartersDate,
    Guid? ApprovedAtHeadquartersBy,
    IEnumerable<BudgetItemDto> Items
);

/// <summary>
/// DTO for budget items
/// </summary>
public record BudgetItemDto(
    Guid Id,
    Guid KpiId,
    int Month,
    decimal? ProposedValue,
    decimal? BudgetValue,
    string? Notes,
    DateTime CreatedAt,
    DateTime? ModifiedAt
);
