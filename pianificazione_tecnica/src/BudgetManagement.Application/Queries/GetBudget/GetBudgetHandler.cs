using AutoMapper;
using BudgetManagement.Domain.Common;
using BudgetManagement.Domain.Repositories;
using BudgetManagement.Domain.ValueObjects;
using MediatR;
using Microsoft.Extensions.Logging;

namespace BudgetManagement.Application.Queries.GetBudget;

/// <summary>
/// Handler for the get budget query
/// </summary>
public class GetBudgetHandler : IRequestHandler<GetBudgetQuery, Result<GetBudgetResponse>>
{
    private readonly IBudgetRepository _budgetRepository;
    private readonly IMapper _mapper;
    private readonly ILogger<GetBudgetHandler> _logger;

    public GetBudgetHandler(
        IBudgetRepository budgetRepository,
        IMapper mapper,
        ILogger<GetBudgetHandler> logger)
    {
        _budgetRepository = budgetRepository;
        _mapper = mapper;
        _logger = logger;
    }

    public async Task<Result<GetBudgetResponse>> Handle(
        GetBudgetQuery request,
        CancellationToken cancellationToken)
    {
        try
        {
            var budgetId = BudgetId.From(request.BudgetId);
            var budget = await _budgetRepository.GetByIdAsync(budgetId, cancellationToken);

            if (budget == null)
            {
                _logger.LogWarning("Budget not found with ID: {BudgetId}", request.BudgetId);
                return Result.Failure<GetBudgetResponse>("Budget not found");
            }

            var response = _mapper.Map<GetBudgetResponse>(budget);
            return Result.Success(response);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving budget with ID: {BudgetId}", request.BudgetId);
            return Result.Failure<GetBudgetResponse>("An error occurred while retrieving the budget");
        }
    }
}
