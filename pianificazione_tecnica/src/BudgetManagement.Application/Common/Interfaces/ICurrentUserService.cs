using BudgetManagement.Domain.ValueObjects;

namespace BudgetManagement.Application.Common.Interfaces;

/// <summary>
/// Service for accessing current user information
/// </summary>
public interface ICurrentUserService
{
    /// <summary>
    /// Gets the current user identifier
    /// </summary>
    UserId? UserId { get; }

    /// <summary>
    /// Gets the current user name
    /// </summary>
    string? UserName { get; }

    /// <summary>
    /// Checks if the current user is authenticated
    /// </summary>
    bool IsAuthenticated { get; }

    /// <summary>
    /// Checks if the current user has the specified role
    /// </summary>
    bool IsInRole(string role);
}
