using AutoMapper;
using BudgetManagement.Application.Queries.GetBudget;
using BudgetManagement.Domain.Entities;

namespace BudgetManagement.Application.Common.Mappings;

/// <summary>
/// AutoMapper profile for budget-related mappings
/// </summary>
public class BudgetMappingProfile : Profile
{
    public BudgetMappingProfile()
    {
        CreateMap<AnnualBudget, GetBudgetResponse>()
            .ForMember(dest => dest.Id, opt => opt.MapFrom(src => src.Id.Value))
            .ForMember(dest => dest.PlantId, opt => opt.MapFrom(src => src.PlantId.Value))
            .ForMember(dest => dest.ProductionUnitId, opt => opt.MapFrom(src => src.ProductionUnitId.Value))
            .ForMember(dest => dest.Year, opt => opt.MapFrom(src => src.Year.Value))
            .ForMember(dest => dest.Status, opt => opt.MapFrom(src => src.Status.ToString()))
            .ForMember(dest => dest.CreatedBy, opt => opt.MapFrom(src => src.CreatedBy.Value))
            .ForMember(dest => dest.MaintenancePlanId, opt => opt.MapFrom(src => src.MaintenancePlanId != null ? src.MaintenancePlanId.Value : (Guid?)null))
            .ForMember(dest => dest.ApprovedAtPlantBy, opt => opt.MapFrom(src => src.ApprovedAtPlantBy != null ? src.ApprovedAtPlantBy.Value : (Guid?)null))
            .ForMember(dest => dest.ApprovedAtHeadquartersBy, opt => opt.MapFrom(src => src.ApprovedAtHeadquartersBy != null ? src.ApprovedAtHeadquartersBy.Value : (Guid?)null));

        CreateMap<BudgetItem, BudgetItemDto>()
            .ForMember(dest => dest.Id, opt => opt.MapFrom(src => src.Id.Value))
            .ForMember(dest => dest.KpiId, opt => opt.MapFrom(src => src.KpiId.Value));
    }
}
