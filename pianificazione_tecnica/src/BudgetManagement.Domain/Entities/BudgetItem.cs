using BudgetManagement.Domain.Common;
using BudgetManagement.Domain.ValueObjects;

namespace BudgetManagement.Domain.Entities;

/// <summary>
/// Represents an item within a budget
/// </summary>
public class BudgetItem : Entity<BudgetItemId>
{
    /// <summary>
    /// Gets the KPI identifier
    /// </summary>
    public KpiId KpiId { get; private set; } = default!;

    /// <summary>
    /// Gets the month (1-12)
    /// </summary>
    public int Month { get; private set; }

    /// <summary>
    /// Gets the proposed value
    /// </summary>
    public decimal? ProposedValue { get; private set; }

    /// <summary>
    /// Gets the budget value
    /// </summary>
    public decimal? BudgetValue { get; private set; }

    /// <summary>
    /// Gets the notes for this item
    /// </summary>
    public string? Notes { get; private set; }

    // Private constructor for EF Core
    private BudgetItem() { }

    /// <summary>
    /// Creates a new budget item
    /// </summary>
    public static BudgetItem Create(
        KpiId kpiId,
        int month,
        decimal? proposedValue = null,
        decimal? budgetValue = null,
        string? notes = null)
    {
        if (month < 1 || month > 12)
            throw new ArgumentException("Month must be between 1 and 12", nameof(month));

        return new BudgetItem
        {
            Id = BudgetItemId.New(),
            KpiId = kpiId,
            Month = month,
            ProposedValue = proposedValue,
            BudgetValue = budgetValue,
            Notes = notes
        };
    }

    /// <summary>
    /// Updates the proposed value
    /// </summary>
    public void UpdateProposedValue(decimal? value)
    {
        ProposedValue = value;
        MarkAsModified();
    }

    /// <summary>
    /// Updates the budget value
    /// </summary>
    public void UpdateBudgetValue(decimal? value)
    {
        BudgetValue = value;
        MarkAsModified();
    }

    /// <summary>
    /// Updates the notes
    /// </summary>
    public void UpdateNotes(string? notes)
    {
        Notes = notes;
        MarkAsModified();
    }
}
