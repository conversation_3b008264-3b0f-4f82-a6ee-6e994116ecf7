using BudgetManagement.Domain.Common;
using BudgetManagement.Domain.Enums;
using BudgetManagement.Domain.Events;
using BudgetManagement.Domain.ValueObjects;

namespace BudgetManagement.Domain.Entities;

/// <summary>
/// Represents an annual budget for a plant
/// </summary>
public class AnnualBudget : AggregateRoot<BudgetId>
{
    private readonly List<BudgetItem> _items = new();

    /// <summary>
    /// Gets the plant identifier
    /// </summary>
    public PlantId PlantId { get; private set; } = default!;

    /// <summary>
    /// Gets the production unit identifier
    /// </summary>
    public ProductionUnitId ProductionUnitId { get; private set; } = default!;

    /// <summary>
    /// Gets the budget year
    /// </summary>
    public Year Year { get; private set; } = default!;

    /// <summary>
    /// Gets the budget status
    /// </summary>
    public BudgetStatus Status { get; private set; }

    /// <summary>
    /// Gets the user who created the budget
    /// </summary>
    public UserId CreatedBy { get; private set; } = default!;

    /// <summary>
    /// Gets the maintenance plan identifier if associated
    /// </summary>
    public MaintenancePlanId? MaintenancePlanId { get; private set; }

    /// <summary>
    /// Gets the budget version
    /// </summary>
    public int Version { get; private set; } = 1;

    /// <summary>
    /// Gets the budget items
    /// </summary>
    public IReadOnlyList<BudgetItem> Items => _items.AsReadOnly();

    /// <summary>
    /// Gets the approval date at plant level
    /// </summary>
    public DateTime? ApprovedAtPlantDate { get; private set; }

    /// <summary>
    /// Gets the user who approved at plant level
    /// </summary>
    public UserId? ApprovedAtPlantBy { get; private set; }

    /// <summary>
    /// Gets the approval date at headquarters level
    /// </summary>
    public DateTime? ApprovedAtHeadquartersDate { get; private set; }

    /// <summary>
    /// Gets the user who approved at headquarters level
    /// </summary>
    public UserId? ApprovedAtHeadquartersBy { get; private set; }

    // Private constructor for EF Core
    private AnnualBudget() { }

    /// <summary>
    /// Creates a new annual budget
    /// </summary>
    public static AnnualBudget Create(
        PlantId plantId,
        ProductionUnitId productionUnitId,
        Year year,
        UserId createdBy)
    {
        var budget = new AnnualBudget
        {
            Id = BudgetId.New(),
            PlantId = plantId,
            ProductionUnitId = productionUnitId,
            Year = year,
            Status = BudgetStatus.Draft,
            CreatedBy = createdBy
        };

        budget.AddDomainEvent(new BudgetCreatedEvent(budget.Id, plantId, year, createdBy));
        return budget;
    }

    /// <summary>
    /// Associates a maintenance plan with this budget
    /// </summary>
    public Result SetMaintenancePlan(MaintenancePlanId maintenancePlanId)
    {
        if (Status != BudgetStatus.Draft)
            return Result.Failure("Cannot modify maintenance plan when budget is not in draft status");

        MaintenancePlanId = maintenancePlanId;
        MarkAsModified();

        AddDomainEvent(new BudgetMaintenancePlanAssociatedEvent(Id, maintenancePlanId));
        return Result.Success();
    }

    /// <summary>
    /// Submits the budget for approval
    /// </summary>
    public Result Submit(UserId userId)
    {
        if (Status != BudgetStatus.Draft)
            return Result.Failure("Budget can only be submitted from Draft status");

        if (!_items.Any())
            return Result.Failure("Cannot submit budget without items");

        Status = BudgetStatus.Submitted;
        MarkAsModified();

        AddDomainEvent(new BudgetSubmittedEvent(Id, userId));
        return Result.Success();
    }

    /// <summary>
    /// Approves the budget at plant level
    /// </summary>
    public Result ApproveAtPlant(UserId userId)
    {
        if (Status != BudgetStatus.Submitted)
            return Result.Failure("Budget can only be approved at plant level from Submitted status");

        Status = BudgetStatus.ApprovedPlant;
        ApprovedAtPlantDate = DateTime.UtcNow;
        ApprovedAtPlantBy = userId;
        MarkAsModified();

        AddDomainEvent(new BudgetApprovedAtPlantEvent(Id, userId));
        return Result.Success();
    }

    /// <summary>
    /// Approves the budget at headquarters level
    /// </summary>
    public Result ApproveAtHeadquarters(UserId userId)
    {
        if (Status != BudgetStatus.InApprovalHeadquarters)
            return Result.Failure("Budget can only be approved at headquarters from InApprovalHeadquarters status");

        Status = BudgetStatus.ApprovedHeadquarters;
        ApprovedAtHeadquartersDate = DateTime.UtcNow;
        ApprovedAtHeadquartersBy = userId;
        MarkAsModified();

        AddDomainEvent(new BudgetApprovedAtHeadquartersEvent(Id, userId));
        return Result.Success();
    }

    /// <summary>
    /// Rejects the budget
    /// </summary>
    public Result Reject(UserId userId, string reason)
    {
        if (Status == BudgetStatus.Draft || Status == BudgetStatus.Rejected)
            return Result.Failure("Cannot reject budget in current status");

        Status = BudgetStatus.Rejected;
        MarkAsModified();

        AddDomainEvent(new BudgetRejectedEvent(Id, userId, reason));
        return Result.Success();
    }

    /// <summary>
    /// Sets the budget as reference for the year
    /// </summary>
    public Result SetAsReference(UserId userId)
    {
        if (Status != BudgetStatus.ApprovedHeadquarters)
            return Result.Failure("Only approved budgets can be set as reference");

        Status = BudgetStatus.Reference;
        MarkAsModified();

        AddDomainEvent(new BudgetSetAsReferenceEvent(Id, userId));
        return Result.Success();
    }

    /// <summary>
    /// Adds a budget item
    /// </summary>
    public Result AddItem(BudgetItem item)
    {
        if (Status != BudgetStatus.Draft)
            return Result.Failure("Cannot add items when budget is not in draft status");

        _items.Add(item);
        MarkAsModified();

        return Result.Success();
    }

    /// <summary>
    /// Removes a budget item
    /// </summary>
    public Result RemoveItem(BudgetItem item)
    {
        if (Status != BudgetStatus.Draft)
            return Result.Failure("Cannot remove items when budget is not in draft status");

        _items.Remove(item);
        MarkAsModified();

        return Result.Success();
    }
}
