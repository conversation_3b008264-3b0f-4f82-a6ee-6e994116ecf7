using BudgetManagement.Domain.ValueObjects;
using MediatR;

namespace BudgetManagement.Domain.Events;

/// <summary>
/// Domain event raised when a maintenance plan is associated with a budget
/// </summary>
public record BudgetMaintenancePlanAssociatedEvent(
    BudgetId BudgetId,
    MaintenancePlanId MaintenancePlanId,
    DateTime OccurredAt = default
) : INotification
{
    public DateTime OccurredAt { get; } = OccurredAt == default ? DateTime.UtcNow : OccurredAt;
}
