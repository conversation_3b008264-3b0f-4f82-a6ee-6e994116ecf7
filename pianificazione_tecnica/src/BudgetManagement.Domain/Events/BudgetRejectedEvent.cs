using BudgetManagement.Domain.ValueObjects;
using MediatR;

namespace BudgetManagement.Domain.Events;

/// <summary>
/// Domain event raised when a budget is rejected
/// </summary>
public record BudgetRejectedEvent(
    BudgetId BudgetId,
    UserId RejectedBy,
    string Reason,
    DateTime OccurredAt = default
) : INotification
{
    public DateTime OccurredAt { get; } = OccurredAt == default ? DateTime.UtcNow : OccurredAt;
}
