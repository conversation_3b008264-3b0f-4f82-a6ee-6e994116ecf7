using BudgetManagement.Domain.ValueObjects;
using MediatR;

namespace BudgetManagement.Domain.Events;

/// <summary>
/// Domain event raised when a budget is submitted for approval
/// </summary>
public record BudgetSubmittedEvent(
    BudgetId BudgetId,
    UserId SubmittedBy,
    DateTime OccurredAt = default
) : INotification
{
    public DateTime OccurredAt { get; } = OccurredAt == default ? DateTime.UtcNow : OccurredAt;
}
