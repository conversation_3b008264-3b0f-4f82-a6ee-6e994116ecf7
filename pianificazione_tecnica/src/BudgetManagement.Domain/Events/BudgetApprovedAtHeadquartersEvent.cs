using BudgetManagement.Domain.ValueObjects;
using MediatR;

namespace BudgetManagement.Domain.Events;

/// <summary>
/// Domain event raised when a budget is approved at headquarters level
/// </summary>
public record BudgetApprovedAtHeadquartersEvent(
    BudgetId BudgetId,
    UserId ApprovedBy,
    DateTime OccurredAt = default
) : INotification
{
    public DateTime OccurredAt { get; } = OccurredAt == default ? DateTime.UtcNow : OccurredAt;
}
