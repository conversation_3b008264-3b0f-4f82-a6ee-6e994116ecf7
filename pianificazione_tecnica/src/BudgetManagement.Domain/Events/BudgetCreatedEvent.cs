using BudgetManagement.Domain.ValueObjects;
using MediatR;

namespace BudgetManagement.Domain.Events;

/// <summary>
/// Domain event raised when a budget is created
/// </summary>
public record BudgetCreatedEvent(
    BudgetId BudgetId,
    PlantId PlantId,
    Year Year,
    UserId CreatedBy,
    DateTime OccurredAt = default
) : INotification
{
    public DateTime OccurredAt { get; } = OccurredAt == default ? DateTime.UtcNow : OccurredAt;
}
