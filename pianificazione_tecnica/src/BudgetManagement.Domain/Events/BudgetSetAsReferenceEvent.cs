using BudgetManagement.Domain.ValueObjects;
using MediatR;

namespace BudgetManagement.Domain.Events;

/// <summary>
/// Domain event raised when a budget is set as reference
/// </summary>
public record BudgetSetAsReferenceEvent(
    BudgetId BudgetId,
    UserId SetBy,
    DateTime OccurredAt = default
) : INotification
{
    public DateTime OccurredAt { get; } = OccurredAt == default ? DateTime.UtcNow : OccurredAt;
}
