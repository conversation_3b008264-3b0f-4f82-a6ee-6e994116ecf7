using BudgetManagement.Domain.Entities;
using BudgetManagement.Domain.Enums;
using BudgetManagement.Domain.ValueObjects;

namespace BudgetManagement.Domain.Repositories;

/// <summary>
/// Repository interface for budget operations
/// </summary>
public interface IBudgetRepository
{
    /// <summary>
    /// Gets a budget by its identifier
    /// </summary>
    Task<AnnualBudget?> GetByIdAsync(BudgetId id, CancellationToken cancellationToken = default);

    /// <summary>
    /// Gets budgets by plant and year
    /// </summary>
    Task<IEnumerable<AnnualBudget>> GetByPlantAndYearAsync(PlantId plantId, Year year, CancellationToken cancellationToken = default);

    /// <summary>
    /// Checks if a budget exists for the given plant, production unit and year
    /// </summary>
    Task<bool> ExistsAsync(PlantId plantId, ProductionUnitId productionUnitId, Year year, CancellationToken cancellationToken = default);

    /// <summary>
    /// Adds a new budget
    /// </summary>
    Task AddAsync(AnnualBudget budget, CancellationToken cancellationToken = default);

    /// <summary>
    /// Updates an existing budget
    /// </summary>
    Task UpdateAsync(AnnualBudget budget, CancellationToken cancellationToken = default);

    /// <summary>
    /// Removes a budget
    /// </summary>
    Task RemoveAsync(AnnualBudget budget, CancellationToken cancellationToken = default);

    /// <summary>
    /// Gets budgets by year and status
    /// </summary>
    Task<IEnumerable<AnnualBudget>> GetByYearAndStatusAsync(Year year, BudgetStatus status, CancellationToken cancellationToken = default);
}
