using BudgetManagement.Domain.Common;
using BudgetManagement.Domain.ValueObjects;

namespace BudgetManagement.Domain.Services;

/// <summary>
/// Domain service for budget-related operations
/// </summary>
public interface IBudgetDomainService
{
    /// <summary>
    /// Finds a reference maintenance plan for the given plant and year
    /// </summary>
    Task<Result<MaintenancePlanId>> FindReferencePlanAsync(PlantId plantId, Year year);

    /// <summary>
    /// Checks if a budget can be created for the given plant and year
    /// </summary>
    Task<bool> CanCreateBudgetAsync(PlantId plantId, Year year);

    /// <summary>
    /// Validates if a production unit belongs to the specified plant
    /// </summary>
    Task<bool> IsProductionUnitValidForPlantAsync(ProductionUnitId productionUnitId, PlantId plantId);

    /// <summary>
    /// Checks if a production unit is active for the given year
    /// </summary>
    Task<bool> IsProductionUnitActiveAsync(ProductionUnitId productionUnitId, Year year);
}
