using BudgetManagement.Domain.Common;

namespace BudgetManagement.Domain.ValueObjects;

/// <summary>
/// Strongly typed identifier for Plant entities
/// </summary>
public record PlantId(Guid Value) : IStronglyTypedId<Guid>
{
    /// <summary>
    /// Creates a new plant identifier
    /// </summary>
    public static PlantId New() => new(Guid.NewGuid());

    /// <summary>
    /// Creates a plant identifier from a GUID
    /// </summary>
    public static PlantId From(Guid value) => new(value);

    /// <summary>
    /// Implicitly converts a PlantId to a Guid
    /// </summary>
    public static implicit operator Guid(PlantId id) => id.Value;

    /// <summary>
    /// Implicitly converts a Guid to a PlantId
    /// </summary>
    public static implicit operator PlantId(Guid value) => new(value);

    public override string ToString() => Value.ToString();
}
