using BudgetManagement.Domain.Common;

namespace BudgetManagement.Domain.ValueObjects;

/// <summary>
/// Strongly typed identifier for Budget entities
/// </summary>
public record BudgetId(Guid Value) : IStronglyTypedId<Guid>
{
    /// <summary>
    /// Creates a new budget identifier
    /// </summary>
    public static BudgetId New() => new(Guid.NewGuid());

    /// <summary>
    /// Creates a budget identifier from a GUID
    /// </summary>
    public static BudgetId From(Guid value) => new(value);

    /// <summary>
    /// Implicitly converts a BudgetId to a Guid
    /// </summary>
    public static implicit operator Guid(BudgetId id) => id.Value;

    /// <summary>
    /// Implicitly converts a Guid to a BudgetId
    /// </summary>
    public static implicit operator BudgetId(Guid value) => new(value);

    public override string ToString() => Value.ToString();
}
