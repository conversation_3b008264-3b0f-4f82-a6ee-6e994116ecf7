using BudgetManagement.Domain.Common;

namespace BudgetManagement.Domain.ValueObjects;

/// <summary>
/// Strongly typed identifier for User entities
/// </summary>
public record UserId(Guid Value) : IStronglyTypedId<Guid>
{
    /// <summary>
    /// Creates a new user identifier
    /// </summary>
    public static UserId New() => new(Guid.NewGuid());

    /// <summary>
    /// Creates a user identifier from a GUID
    /// </summary>
    public static UserId From(Guid value) => new(value);

    /// <summary>
    /// Implicitly converts a UserId to a Guid
    /// </summary>
    public static implicit operator Guid(UserId id) => id.Value;

    /// <summary>
    /// Implicitly converts a Guid to a UserId
    /// </summary>
    public static implicit operator UserId(Guid value) => new(value);

    public override string ToString() => Value.ToString();
}
