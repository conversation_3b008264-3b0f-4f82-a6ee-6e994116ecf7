using BudgetManagement.Domain.Common;

namespace BudgetManagement.Domain.ValueObjects;

/// <summary>
/// Strongly typed identifier for BudgetItem entities
/// </summary>
public record BudgetItemId(Guid Value) : IStronglyTypedId<Guid>
{
    /// <summary>
    /// Creates a new budget item identifier
    /// </summary>
    public static BudgetItemId New() => new(Guid.NewGuid());

    /// <summary>
    /// Creates a budget item identifier from a GUID
    /// </summary>
    public static BudgetItemId From(Guid value) => new(value);

    /// <summary>
    /// Implicitly converts a BudgetItemId to a Guid
    /// </summary>
    public static implicit operator Guid(BudgetItemId id) => id.Value;

    /// <summary>
    /// Implicitly converts a Guid to a BudgetItemId
    /// </summary>
    public static implicit operator BudgetItemId(Guid value) => new(value);

    public override string ToString() => Value.ToString();
}
