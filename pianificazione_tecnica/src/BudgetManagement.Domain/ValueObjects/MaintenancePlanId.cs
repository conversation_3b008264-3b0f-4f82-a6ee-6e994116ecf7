using BudgetManagement.Domain.Common;

namespace BudgetManagement.Domain.ValueObjects;

/// <summary>
/// Strongly typed identifier for MaintenancePlan entities
/// </summary>
public record MaintenancePlanId(Guid Value) : IStronglyTypedId<Guid>
{
    /// <summary>
    /// Creates a new maintenance plan identifier
    /// </summary>
    public static MaintenancePlanId New() => new(Guid.NewGuid());

    /// <summary>
    /// Creates a maintenance plan identifier from a GUID
    /// </summary>
    public static MaintenancePlanId From(Guid value) => new(value);

    /// <summary>
    /// Implicitly converts a MaintenancePlanId to a Guid
    /// </summary>
    public static implicit operator Guid(MaintenancePlanId id) => id.Value;

    /// <summary>
    /// Implicitly converts a Guid to a MaintenancePlanId
    /// </summary>
    public static implicit operator MaintenancePlanId(Guid value) => new(value);

    public override string ToString() => Value.ToString();
}
