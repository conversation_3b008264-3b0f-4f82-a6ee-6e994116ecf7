using BudgetManagement.Domain.Common;

namespace BudgetManagement.Domain.ValueObjects;

/// <summary>
/// Strongly typed identifier for KPI entities
/// </summary>
public record KpiId(Guid Value) : IStronglyTypedId<Guid>
{
    /// <summary>
    /// Creates a new KPI identifier
    /// </summary>
    public static KpiId New() => new(Guid.NewGuid());

    /// <summary>
    /// Creates a KPI identifier from a GUID
    /// </summary>
    public static KpiId From(Guid value) => new(value);

    /// <summary>
    /// Implicitly converts a KpiId to a Guid
    /// </summary>
    public static implicit operator Guid(KpiId id) => id.Value;

    /// <summary>
    /// Implicitly converts a Guid to a KpiId
    /// </summary>
    public static implicit operator KpiId(Guid value) => new(value);

    public override string ToString() => Value.ToString();
}
