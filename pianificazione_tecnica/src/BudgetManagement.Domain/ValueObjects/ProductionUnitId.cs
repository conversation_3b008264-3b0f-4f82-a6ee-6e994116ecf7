using BudgetManagement.Domain.Common;

namespace BudgetManagement.Domain.ValueObjects;

/// <summary>
/// Strongly typed identifier for ProductionUnit entities
/// </summary>
public record ProductionUnitId(Guid Value) : IStronglyTypedId<Guid>
{
    /// <summary>
    /// Creates a new production unit identifier
    /// </summary>
    public static ProductionUnitId New() => new(Guid.NewGuid());

    /// <summary>
    /// Creates a production unit identifier from a GUID
    /// </summary>
    public static ProductionUnitId From(Guid value) => new(value);

    /// <summary>
    /// Implicitly converts a ProductionUnitId to a Guid
    /// </summary>
    public static implicit operator Guid(ProductionUnitId id) => id.Value;

    /// <summary>
    /// Implicitly converts a Guid to a ProductionUnitId
    /// </summary>
    public static implicit operator ProductionUnitId(Guid value) => new(value);

    public override string ToString() => Value.ToString();
}
