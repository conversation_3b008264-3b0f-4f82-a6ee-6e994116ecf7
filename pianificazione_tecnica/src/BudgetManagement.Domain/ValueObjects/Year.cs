namespace BudgetManagement.Domain.ValueObjects;

/// <summary>
/// Value object representing a year
/// </summary>
public record Year
{
    public int Value { get; }

    public Year(int value)
    {
        if (value < 2020 || value > 2050)
            throw new ArgumentException("Year must be between 2020 and 2050", nameof(value));

        Value = value;
    }

    /// <summary>
    /// Gets the current year
    /// </summary>
    public static Year Current => new(DateTime.UtcNow.Year);

    /// <summary>
    /// Gets the next year
    /// </summary>
    public static Year Next => new(DateTime.UtcNow.Year + 1);

    /// <summary>
    /// Implicitly converts an int to a Year
    /// </summary>
    public static implicit operator Year(int value) => new(value);

    /// <summary>
    /// Implicitly converts a Year to an int
    /// </summary>
    public static implicit operator int(Year year) => year.Value;

    public override string ToString() => Value.ToString();
}
