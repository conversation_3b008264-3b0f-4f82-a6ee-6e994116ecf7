namespace BudgetManagement.Domain.Enums;

/// <summary>
/// Represents the status of a budget
/// </summary>
public enum BudgetStatus
{
    /// <summary>
    /// Budget is in draft state and can be modified
    /// </summary>
    Draft = 0,

    /// <summary>
    /// Budget is submitted for approval
    /// </summary>
    Submitted = 1,

    /// <summary>
    /// Budget is approved at plant level
    /// </summary>
    ApprovedPlant = 2,

    /// <summary>
    /// Budget is in approval process at headquarters level
    /// </summary>
    InApprovalHeadquarters = 3,

    /// <summary>
    /// Budget is approved at headquarters level
    /// </summary>
    ApprovedHeadquarters = 4,

    /// <summary>
    /// Budget is set as reference for the year
    /// </summary>
    Reference = 5,

    /// <summary>
    /// Budget has been rejected
    /// </summary>
    Rejected = 6
}
