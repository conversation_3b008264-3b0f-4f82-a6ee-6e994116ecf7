version: '3.8'

services:
  # PostgreSQL Database for development
  postgres:
    image: postgres:16-alpine
    container_name: budget-postgres-dev
    environment:
      - POSTGRES_DB=budget_management
      - POSTGRES_USER=postgres
      - POSTGRES_PASSWORD=postgres
    ports:
      - "5432:5432"
    volumes:
      - postgres_dev_data:/var/lib/postgresql/data
    networks:
      - budget-network

  # Redis for development
  redis:
    image: redis:7-alpine
    container_name: budget-redis-dev
    ports:
      - "6379:6379"
    volumes:
      - redis_dev_data:/data
    networks:
      - budget-network

volumes:
  postgres_dev_data:
  redis_dev_data:

networks:
  budget-network:
    driver: bridge
