using BudgetManagement.Domain.Entities;
using BudgetManagement.Domain.Enums;
using BudgetManagement.Domain.Repositories;
using BudgetManagement.Domain.ValueObjects;
using BudgetManagement.Infrastructure.Persistence;
using Microsoft.EntityFrameworkCore;

namespace BudgetManagement.Infrastructure.Repositories;

/// <summary>
/// Repository implementation for budget operations using Entity Framework
/// </summary>
public class BudgetRepository : IBudgetRepository
{
    private readonly BudgetDbContext _context;

    public BudgetRepository(BudgetDbContext context)
    {
        _context = context;
    }

    public async Task<AnnualBudget?> GetByIdAsync(BudgetId id, CancellationToken cancellationToken = default)
    {
        return await _context.Budgets
            .Include(b => b.Items)
            .FirstOrDefaultAsync(b => b.Id == id, cancellationToken);
    }

    public async Task<AnnualBudget?> GetByPlantAndYearAsync(
        PlantId plantId,
        Year year,
        CancellationToken cancellationToken = default)
    {
        return await _context.Budgets
            .Include(b => b.Items)
            .FirstOrDefaultAsync(b => b.PlantId == plantId && b.Year == year, cancellationToken);
    }

    public async Task<bool> ExistsAsync(
        PlantId plantId, 
        ProductionUnitId productionUnitId, 
        Year year, 
        CancellationToken cancellationToken = default)
    {
        return await _context.Budgets
            .AnyAsync(b => b.PlantId == plantId && 
                          b.ProductionUnitId == productionUnitId && 
                          b.Year == year, 
                     cancellationToken);
    }

    public async Task<IEnumerable<AnnualBudget>> GetAllAsync(CancellationToken cancellationToken = default)
    {
        return await _context.Budgets
            .Include(b => b.Items)
            .ToListAsync(cancellationToken);
    }

    public async Task<IEnumerable<AnnualBudget>> GetByFiltersAsync(
        Year? year = null,
        BudgetStatus? status = null,
        CancellationToken cancellationToken = default)
    {
        var query = _context.Budgets.Include(b => b.Items).AsQueryable();

        if (year.HasValue)
            query = query.Where(b => b.Year == year.Value);

        if (status.HasValue)
            query = query.Where(b => b.Status == status.Value);

        return await query.ToListAsync(cancellationToken);
    }

    public async Task AddAsync(AnnualBudget budget, CancellationToken cancellationToken = default)
    {
        await _context.Budgets.AddAsync(budget, cancellationToken);
    }

    public void Update(AnnualBudget budget)
    {
        _context.Budgets.Update(budget);
    }

    public void Remove(AnnualBudget budget)
    {
        _context.Budgets.Remove(budget);
    }

    public Task UpdateAsync(AnnualBudget budget, CancellationToken cancellationToken = default)
    {
        _context.Budgets.Update(budget);
        return Task.CompletedTask;
    }

    public Task RemoveAsync(AnnualBudget budget, CancellationToken cancellationToken = default)
    {
        _context.Budgets.Remove(budget);
        return Task.CompletedTask;
    }

    public async Task<IEnumerable<AnnualBudget>> GetByYearAndStatusAsync(
        Year year, 
        BudgetStatus status, 
        CancellationToken cancellationToken = default)
    {
        return await _context.Budgets
            .Include(b => b.Items)
            .Where(b => b.Year == year && b.Status == status)
            .ToListAsync(cancellationToken);
    }
}
