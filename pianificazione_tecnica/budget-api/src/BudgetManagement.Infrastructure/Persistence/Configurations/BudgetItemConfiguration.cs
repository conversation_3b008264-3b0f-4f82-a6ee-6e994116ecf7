using BudgetManagement.Domain.Entities;
using BudgetManagement.Domain.ValueObjects;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace BudgetManagement.Infrastructure.Persistence.Configurations;

/// <summary>
/// Entity Framework configuration for BudgetItem entity
/// </summary>
public class BudgetItemConfiguration : IEntityTypeConfiguration<BudgetItem>
{
    public void Configure(EntityTypeBuilder<BudgetItem> builder)
    {
        builder.ToTable("budget_items");

        // Primary Key
        builder.HasKey(bi => bi.Id);
        builder.Property(bi => bi.Id)
            .HasConversion(
                id => id.Value,
                value => new BudgetItemId(value))
            .HasColumnName("id");

        // Value Objects
        builder.Property(bi => bi.KpiId)
            .HasColumnName("kpi_id")
            .HasMaxLength(50)
            .IsRequired();

        // Regular Properties
        builder.Property(bi => bi.Month)
            .HasColumnName("month")
            .IsRequired();

        builder.Property(bi => bi.ProposedValue)
            .HasColumnName("proposed_value")
            .HasPrecision(18, 2);

        builder.Property(bi => bi.BudgetValue)
            .HasColumnName("budget_value")
            .HasPrecision(18, 2);

        builder.Property(bi => bi.Notes)
            .HasColumnName("notes")
            .HasMaxLength(1000);

        builder.Property(bi => bi.CreatedAt)
            .HasColumnName("created_at")
            .IsRequired();

        builder.Property(bi => bi.ModifiedAt)
            .HasColumnName("modified_at");

        // Foreign Key
        builder.Property(bi => bi.BudgetId)
            .HasConversion(id => id.Value, value => new BudgetId(value))
            .HasColumnName("budget_id")
            .IsRequired();

        // Indexes
        builder.HasIndex("BudgetId", "Month", "KpiId")
            .IsUnique()
            .HasDatabaseName("ix_budget_items_budget_month_kpi");

        builder.HasIndex(bi => bi.Month)
            .HasDatabaseName("ix_budget_items_month");

        // Constraints
        builder.ToTable(t => t.HasCheckConstraint("ck_budget_items_month", "month >= 1 AND month <= 12"));
    }
}
