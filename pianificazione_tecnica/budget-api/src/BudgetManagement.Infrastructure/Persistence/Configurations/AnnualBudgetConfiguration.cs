using BudgetManagement.Domain.Entities;
using BudgetManagement.Domain.Enums;
using BudgetManagement.Domain.ValueObjects;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace BudgetManagement.Infrastructure.Persistence.Configurations;

/// <summary>
/// Entity Framework configuration for AnnualBudget entity
/// </summary>
public class AnnualBudgetConfiguration : IEntityTypeConfiguration<AnnualBudget>
{
    public void Configure(EntityTypeBuilder<AnnualBudget> builder)
    {
        builder.ToTable("annual_budgets");

        // Primary Key
        builder.HasKey(b => b.Id);
        builder.Property(b => b.Id)
            .HasConversion(
                id => id.Value,
                value => new BudgetId(value))
            .HasColumnName("id");

        // Value Objects
        builder.Property(b => b.PlantId)
            .HasConversion(
                id => id.Value,
                value => new PlantId(value))
            .HasColumnName("plant_id")
            .IsRequired();

        builder.Property(b => b.ProductionUnitId)
            .HasConversion(
                id => id.Value,
                value => new ProductionUnitId(value))
            .HasColumnName("production_unit_id")
            .IsRequired();

        builder.Property(b => b.Year)
            .HasConversion(
                year => year.Value,
                value => new Year(value))
            .HasColumnName("year")
            .IsRequired();

        builder.Property(b => b.CreatedBy)
            .HasConversion(
                id => id.Value,
                value => new UserId(value))
            .HasColumnName("created_by")
            .IsRequired();

        builder.Property(b => b.MaintenancePlanId)
            .HasConversion(
                id => id != null ? id.Value : (Guid?)null,
                value => value.HasValue ? new MaintenancePlanId(value.Value) : null)
            .HasColumnName("maintenance_plan_id");

        builder.Property(b => b.ApprovedAtPlantBy)
            .HasConversion(
                id => id != null ? id.Value : (Guid?)null,
                value => value.HasValue ? new UserId(value.Value) : null)
            .HasColumnName("approved_at_plant_by");

        builder.Property(b => b.ApprovedAtHeadquartersBy)
            .HasConversion(
                id => id != null ? id.Value : (Guid?)null,
                value => value.HasValue ? new UserId(value.Value) : null)
            .HasColumnName("approved_at_headquarters_by");

        // Enums
        builder.Property(b => b.Status)
            .HasConversion<string>()
            .HasColumnName("status")
            .IsRequired();

        // Regular Properties
        builder.Property(b => b.Version)
            .HasColumnName("version")
            .HasDefaultValue(1)
            .IsRequired();

        builder.Property(b => b.CreatedAt)
            .HasColumnName("created_at")
            .IsRequired();

        builder.Property(b => b.ModifiedAt)
            .HasColumnName("modified_at");

        builder.Property(b => b.ApprovedAtPlantDate)
            .HasColumnName("approved_at_plant_date");

        builder.Property(b => b.ApprovedAtHeadquartersDate)
            .HasColumnName("approved_at_headquarters_date");

        // Relationships
        builder.HasMany(b => b.Items)
            .WithOne()
            .HasForeignKey("BudgetId")
            .OnDelete(DeleteBehavior.Cascade);

        // Indexes
        builder.HasIndex(b => new { b.PlantId, b.ProductionUnitId, b.Year })
            .IsUnique()
            .HasDatabaseName("ix_annual_budgets_plant_unit_year");

        builder.HasIndex(b => b.Year)
            .HasDatabaseName("ix_annual_budgets_year");

        builder.HasIndex(b => b.Status)
            .HasDatabaseName("ix_annual_budgets_status");

        // Ignore domain events (they are not persisted)
        builder.Ignore(b => b.DomainEvents);
    }
}
