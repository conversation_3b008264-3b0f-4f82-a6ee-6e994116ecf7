using BudgetManagement.Domain.Entities;
using BudgetManagement.Infrastructure.Persistence.Configurations;
using Microsoft.EntityFrameworkCore;

namespace BudgetManagement.Infrastructure.Persistence;

/// <summary>
/// Database context for budget management
/// </summary>
public class BudgetDbContext : DbContext
{
    public BudgetDbContext(DbContextOptions<BudgetDbContext> options) : base(options)
    {
    }

    public DbSet<AnnualBudget> Budgets { get; set; } = default!;
    public DbSet<BudgetItem> BudgetItems { get; set; } = default!;

    protected override void OnModelCreating(ModelBuilder modelBuilder)
    {
        base.OnModelCreating(modelBuilder);

        // Apply configurations
        modelBuilder.ApplyConfiguration(new AnnualBudgetConfiguration());
        modelBuilder.ApplyConfiguration(new BudgetItemConfiguration());

        // Set default schema
        modelBuilder.HasDefaultSchema("budget");
    }

    protected override void OnConfiguring(DbContextOptionsBuilder optionsBuilder)
    {
        if (!optionsBuilder.IsConfigured)
        {
            // This will be overridden by DI configuration
            optionsBuilder.UseNpgsql();
        }
    }
}
