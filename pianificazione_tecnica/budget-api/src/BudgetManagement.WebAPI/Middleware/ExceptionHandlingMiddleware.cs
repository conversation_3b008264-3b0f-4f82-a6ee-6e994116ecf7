using FluentValidation;
using System.Net;
using System.Text.Json;

namespace BudgetManagement.WebAPI.Middleware;

/// <summary>
/// Middleware for global exception handling
/// </summary>
public class ExceptionHandlingMiddleware
{
    private readonly RequestDelegate _next;
    private readonly ILogger<ExceptionHandlingMiddleware> _logger;

    public ExceptionHandlingMiddleware(RequestDelegate next, ILogger<ExceptionHandlingMiddleware> logger)
    {
        _next = next;
        _logger = logger;
    }

    public async Task InvokeAsync(HttpContext context)
    {
        try
        {
            await _next(context);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "An unhandled exception occurred");
            await HandleExceptionAsync(context, ex);
        }
    }

    private static async Task HandleExceptionAsync(HttpContext context, Exception exception)
    {
        context.Response.ContentType = "application/json";

        var response = new
        {
            title = "An error occurred",
            status = (int)HttpStatusCode.InternalServerError,
            detail = exception.Message,
            traceId = context.TraceIdentifier
        };

        switch (exception)
        {
            case ValidationException validationEx:
                response = new
                {
                    title = "Validation failed",
                    status = (int)HttpStatusCode.BadRequest,
                    detail = "One or more validation errors occurred",
                    errors = validationEx.Errors.GroupBy(e => e.PropertyName)
                        .ToDictionary(g => g.Key, g => g.Select(e => e.ErrorMessage).ToArray()),
                    traceId = context.TraceIdentifier
                };
                context.Response.StatusCode = (int)HttpStatusCode.BadRequest;
                break;

            case ArgumentException:
            case ArgumentNullException:
                response = new
                {
                    title = "Bad request",
                    status = (int)HttpStatusCode.BadRequest,
                    detail = exception.Message,
                    traceId = context.TraceIdentifier
                };
                context.Response.StatusCode = (int)HttpStatusCode.BadRequest;
                break;

            case UnauthorizedAccessException:
                response = new
                {
                    title = "Unauthorized",
                    status = (int)HttpStatusCode.Unauthorized,
                    detail = "You are not authorized to access this resource",
                    traceId = context.TraceIdentifier
                };
                context.Response.StatusCode = (int)HttpStatusCode.Unauthorized;
                break;

            default:
                context.Response.StatusCode = (int)HttpStatusCode.InternalServerError;
                break;
        }

        var jsonResponse = JsonSerializer.Serialize(response, new JsonSerializerOptions
        {
            PropertyNamingPolicy = JsonNamingPolicy.CamelCase
        });

        await context.Response.WriteAsync(jsonResponse);
    }
}
