{"ConnectionStrings": {"DefaultConnection": "Host=localhost;Database=budget_management;Username=********;Password=********", "Redis": "localhost:6379"}, "JwtSettings": {"SecretKey": "your-super-secret-key-that-is-at-least-32-characters-long", "Issuer": "BudgetManagement", "Audience": "BudgetManagement", "ExpirationInMinutes": 60}, "AzureAd": {"Enabled": false, "Instance": "https://login.microsoftonline.com/", "TenantId": "your-tenant-id", "ClientId": "your-client-id"}, "Cors": {"AllowedOrigins": ["http://localhost:3000", "http://localhost:3001", "https://localhost:3000", "https://localhost:3001"]}, "Logging": {"LogLevel": {"Default": "Information", "Microsoft.AspNetCore": "Warning", "Microsoft.EntityFrameworkCore": "Warning"}, "EnableSensitiveDataLogging": false}, "Serilog": {"Using": ["Serilog.Sinks.Console", "Serilog.Sinks.File"], "MinimumLevel": {"Default": "Information", "Override": {"Microsoft": "Warning", "System": "Warning"}}, "WriteTo": [{"Name": "<PERSON><PERSON><PERSON>", "Args": {"outputTemplate": "[{Timestamp:HH:mm:ss} {Level:u3}] {Message:lj} {Properties:j}{NewLine}{Exception}"}}, {"Name": "File", "Args": {"path": "logs/budget-api-.log", "rollingInterval": "Day", "retainedFileCountLimit": 7, "outputTemplate": "[{Timestamp:yyyy-MM-dd HH:mm:ss.fff zzz} {Level:u3}] {Message:lj} {Properties:j}{NewLine}{Exception}"}}], "Enrich": ["FromLogContext", "WithMachineName", "WithThreadId"]}, "AllowedHosts": "*"}