using BudgetManagement.Infrastructure.Persistence;
using Microsoft.EntityFrameworkCore;

namespace BudgetManagement.WebAPI.Extensions;

/// <summary>
/// Extension methods for web application configuration
/// </summary>
public static class WebApplicationExtensions
{
    /// <summary>
    /// Ensures the database is created and migrations are applied
    /// </summary>
    public static async Task EnsureDatabaseCreatedAsync(this WebApplication app)
    {
        using var scope = app.Services.CreateScope();
        var context = scope.ServiceProvider.GetRequiredService<BudgetDbContext>();
        var logger = scope.ServiceProvider.GetRequiredService<ILogger<BudgetDbContext>>();

        try
        {
            logger.LogInformation("Ensuring database is created and up to date...");

            // Check if using in-memory database
            var configuration = app.Services.GetRequiredService<IConfiguration>();
            var useInMemoryDatabase = configuration.GetValue<bool>("UseInMemoryDatabase");

            if (useInMemoryDatabase)
            {
                await context.Database.EnsureCreatedAsync();
                logger.LogInformation("In-memory database created");
            }
            else
            {
                // Apply pending migrations for PostgreSQL
                await context.Database.MigrateAsync();
                logger.LogInformation("Database migrations applied");
            }

            logger.LogInformation("Database is ready");
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "An error occurred while ensuring database is created");
            throw;
        }
    }
}
