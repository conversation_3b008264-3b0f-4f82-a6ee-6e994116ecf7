using BudgetManagement.Infrastructure.Persistence;
using Microsoft.EntityFrameworkCore;

namespace BudgetManagement.WebAPI.Extensions;

/// <summary>
/// Extension methods for web application configuration
/// </summary>
public static class WebApplicationExtensions
{
    /// <summary>
    /// Ensures the database is created and migrations are applied
    /// </summary>
    public static async Task EnsureDatabaseCreatedAsync(this WebApplication app)
    {
        using var scope = app.Services.CreateScope();
        var context = scope.ServiceProvider.GetRequiredService<BudgetDbContext>();
        var logger = scope.ServiceProvider.GetRequiredService<ILogger<BudgetDbContext>>();

        try
        {
            logger.LogInformation("Ensuring database is created and up to date...");
            
            // Apply pending migrations
            await context.Database.MigrateAsync();
            
            logger.LogInformation("Database is ready");
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "An error occurred while ensuring database is created");
            throw;
        }
    }
}
