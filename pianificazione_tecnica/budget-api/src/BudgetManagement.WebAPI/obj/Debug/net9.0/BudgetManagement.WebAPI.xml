<?xml version="1.0"?>
<doc>
    <assembly>
        <name>BudgetManagement.WebAPI</name>
    </assembly>
    <members>
        <member name="T:BudgetManagement.WebAPI.Controllers.BudgetController">
            <summary>
            Controller for budget management operations
            </summary>
        </member>
        <member name="M:BudgetManagement.WebAPI.Controllers.BudgetController.CreateAnnualBudget(BudgetManagement.WebAPI.Controllers.CreateAnnualBudgetRequest)">
            <summary>
            Creates a new annual budget for a plant
            </summary>
            <param name="request">The budget creation request</param>
            <returns>The created budget information</returns>
        </member>
        <member name="M:BudgetManagement.WebAPI.Controllers.BudgetController.GetBudget(System.Guid)">
            <summary>
            Gets a budget by its identifier
            </summary>
            <param name="id">The budget identifier</param>
            <returns>The budget information</returns>
        </member>
        <member name="M:BudgetManagement.WebAPI.Controllers.BudgetController.GetHealth">
            <summary>
            Gets the health status of the budget service
            </summary>
            <returns>Health status</returns>
        </member>
        <member name="T:BudgetManagement.WebAPI.Controllers.CreateAnnualBudgetRequest">
            <summary>
            Request model for creating an annual budget
            </summary>
        </member>
        <member name="M:BudgetManagement.WebAPI.Controllers.CreateAnnualBudgetRequest.#ctor(System.Guid,System.Guid,System.Int32)">
            <summary>
            Request model for creating an annual budget
            </summary>
        </member>
        <member name="T:BudgetManagement.WebAPI.Extensions.ServiceCollectionExtensions">
            <summary>
            Extension methods for service collection configuration
            </summary>
        </member>
        <member name="M:BudgetManagement.WebAPI.Extensions.ServiceCollectionExtensions.AddWebApiServices(Microsoft.Extensions.DependencyInjection.IServiceCollection,Microsoft.Extensions.Configuration.IConfiguration)">
            <summary>
            Adds Web API specific services
            </summary>
        </member>
        <member name="T:BudgetManagement.WebAPI.Extensions.WebApplicationExtensions">
            <summary>
            Extension methods for web application configuration
            </summary>
        </member>
        <member name="M:BudgetManagement.WebAPI.Extensions.WebApplicationExtensions.EnsureDatabaseCreatedAsync(Microsoft.AspNetCore.Builder.WebApplication)">
            <summary>
            Ensures the database is created and migrations are applied
            </summary>
        </member>
        <member name="T:BudgetManagement.WebAPI.Middleware.ExceptionHandlingMiddleware">
            <summary>
            Middleware for global exception handling
            </summary>
        </member>
        <member name="T:BudgetManagement.WebAPI.Middleware.RequestLoggingMiddleware">
            <summary>
            Middleware for request/response logging
            </summary>
        </member>
        <member name="T:BudgetManagement.WebAPI.Services.CurrentUserService">
            <summary>
            Service for accessing current user information from HTTP context
            </summary>
        </member>
    </members>
</doc>
