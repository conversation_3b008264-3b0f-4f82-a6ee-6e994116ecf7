{"format": 1, "restore": {"/Users/<USER>/Desktop/A2A/NDUE/NBDO-devel-new-layout-2017/pianificazione_tecnica/budget-api/src/BudgetManagement.WebAPI/BudgetManagement.WebAPI.csproj": {}}, "projects": {"/Users/<USER>/Desktop/A2A/NDUE/NBDO-devel-new-layout-2017/pianificazione_tecnica/budget-api/src/BudgetManagement.Application/BudgetManagement.Application.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "/Users/<USER>/Desktop/A2A/NDUE/NBDO-devel-new-layout-2017/pianificazione_tecnica/budget-api/src/BudgetManagement.Application/BudgetManagement.Application.csproj", "projectName": "BudgetManagement.Application", "projectPath": "/Users/<USER>/Desktop/A2A/NDUE/NBDO-devel-new-layout-2017/pianificazione_tecnica/budget-api/src/BudgetManagement.Application/BudgetManagement.Application.csproj", "packagesPath": "/Users/<USER>/.nuget/packages/", "outputPath": "/Users/<USER>/Desktop/A2A/NDUE/NBDO-devel-new-layout-2017/pianificazione_tecnica/budget-api/src/BudgetManagement.Application/obj/", "projectStyle": "PackageReference", "configFilePaths": ["/Users/<USER>/.nuget/NuGet/NuGet.Config"], "originalTargetFrameworks": ["net9.0"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "projectReferences": {"/Users/<USER>/Desktop/A2A/NDUE/NBDO-devel-new-layout-2017/pianificazione_tecnica/budget-api/src/BudgetManagement.Domain/BudgetManagement.Domain.csproj": {"projectPath": "/Users/<USER>/Desktop/A2A/NDUE/NBDO-devel-new-layout-2017/pianificazione_tecnica/budget-api/src/BudgetManagement.Domain/BudgetManagement.Domain.csproj"}}}}, "warningProperties": {"allWarningsAsErrors": true}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.200"}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "dependencies": {"AutoMapper": {"target": "Package", "version": "[13.0.1, )"}, "FluentValidation": {"target": "Package", "version": "[11.9.0, )"}, "FluentValidation.DependencyInjectionExtensions": {"target": "Package", "version": "[11.9.0, )"}, "MediatR": {"target": "Package", "version": "[12.2.0, )"}, "Microsoft.Extensions.Logging.Abstractions": {"target": "Package", "version": "[9.0.0, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "/usr/local/share/dotnet/sdk/9.0.200/PortableRuntimeIdentifierGraph.json"}}}, "/Users/<USER>/Desktop/A2A/NDUE/NBDO-devel-new-layout-2017/pianificazione_tecnica/budget-api/src/BudgetManagement.Domain/BudgetManagement.Domain.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "/Users/<USER>/Desktop/A2A/NDUE/NBDO-devel-new-layout-2017/pianificazione_tecnica/budget-api/src/BudgetManagement.Domain/BudgetManagement.Domain.csproj", "projectName": "BudgetManagement.Domain", "projectPath": "/Users/<USER>/Desktop/A2A/NDUE/NBDO-devel-new-layout-2017/pianificazione_tecnica/budget-api/src/BudgetManagement.Domain/BudgetManagement.Domain.csproj", "packagesPath": "/Users/<USER>/.nuget/packages/", "outputPath": "/Users/<USER>/Desktop/A2A/NDUE/NBDO-devel-new-layout-2017/pianificazione_tecnica/budget-api/src/BudgetManagement.Domain/obj/", "projectStyle": "PackageReference", "configFilePaths": ["/Users/<USER>/.nuget/NuGet/NuGet.Config"], "originalTargetFrameworks": ["net9.0"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "projectReferences": {}}}, "warningProperties": {"allWarningsAsErrors": true}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.200"}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "dependencies": {"MediatR": {"target": "Package", "version": "[12.2.0, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "/usr/local/share/dotnet/sdk/9.0.200/PortableRuntimeIdentifierGraph.json"}}}, "/Users/<USER>/Desktop/A2A/NDUE/NBDO-devel-new-layout-2017/pianificazione_tecnica/budget-api/src/BudgetManagement.Infrastructure/BudgetManagement.Infrastructure.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "/Users/<USER>/Desktop/A2A/NDUE/NBDO-devel-new-layout-2017/pianificazione_tecnica/budget-api/src/BudgetManagement.Infrastructure/BudgetManagement.Infrastructure.csproj", "projectName": "BudgetManagement.Infrastructure", "projectPath": "/Users/<USER>/Desktop/A2A/NDUE/NBDO-devel-new-layout-2017/pianificazione_tecnica/budget-api/src/BudgetManagement.Infrastructure/BudgetManagement.Infrastructure.csproj", "packagesPath": "/Users/<USER>/.nuget/packages/", "outputPath": "/Users/<USER>/Desktop/A2A/NDUE/NBDO-devel-new-layout-2017/pianificazione_tecnica/budget-api/src/BudgetManagement.Infrastructure/obj/", "projectStyle": "PackageReference", "configFilePaths": ["/Users/<USER>/.nuget/NuGet/NuGet.Config"], "originalTargetFrameworks": ["net9.0"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "projectReferences": {"/Users/<USER>/Desktop/A2A/NDUE/NBDO-devel-new-layout-2017/pianificazione_tecnica/budget-api/src/BudgetManagement.Application/BudgetManagement.Application.csproj": {"projectPath": "/Users/<USER>/Desktop/A2A/NDUE/NBDO-devel-new-layout-2017/pianificazione_tecnica/budget-api/src/BudgetManagement.Application/BudgetManagement.Application.csproj"}, "/Users/<USER>/Desktop/A2A/NDUE/NBDO-devel-new-layout-2017/pianificazione_tecnica/budget-api/src/BudgetManagement.Domain/BudgetManagement.Domain.csproj": {"projectPath": "/Users/<USER>/Desktop/A2A/NDUE/NBDO-devel-new-layout-2017/pianificazione_tecnica/budget-api/src/BudgetManagement.Domain/BudgetManagement.Domain.csproj"}}}}, "warningProperties": {"allWarningsAsErrors": true}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.200"}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "dependencies": {"Microsoft.EntityFrameworkCore": {"target": "Package", "version": "[9.0.0, )"}, "Microsoft.EntityFrameworkCore.Design": {"target": "Package", "version": "[9.0.0, )"}, "Microsoft.EntityFrameworkCore.InMemory": {"target": "Package", "version": "[9.0.0, )"}, "Microsoft.EntityFrameworkCore.Sqlite": {"target": "Package", "version": "[9.0.0, )"}, "Microsoft.Extensions.Caching.StackExchangeRedis": {"target": "Package", "version": "[9.0.0, )"}, "Microsoft.Extensions.Configuration.Abstractions": {"target": "Package", "version": "[9.0.0, )"}, "Microsoft.Extensions.Configuration.Binder": {"target": "Package", "version": "[9.0.0, )"}, "Microsoft.Extensions.DependencyInjection.Abstractions": {"target": "Package", "version": "[9.0.0, )"}, "Npgsql.EntityFrameworkCore.PostgreSQL": {"target": "Package", "version": "[9.0.0, )"}, "Serilog": {"target": "Package", "version": "[3.1.1, )"}, "Serilog.Extensions.Hosting": {"target": "Package", "version": "[8.0.0, )"}, "Serilog.Settings.Configuration": {"target": "Package", "version": "[8.0.4, )"}, "Serilog.Sinks.Console": {"target": "Package", "version": "[5.0.1, )"}, "Serilog.Sinks.File": {"target": "Package", "version": "[5.0.0, )"}, "StackExchange.Redis": {"target": "Package", "version": "[2.7.33, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "/usr/local/share/dotnet/sdk/9.0.200/PortableRuntimeIdentifierGraph.json"}}}, "/Users/<USER>/Desktop/A2A/NDUE/NBDO-devel-new-layout-2017/pianificazione_tecnica/budget-api/src/BudgetManagement.WebAPI/BudgetManagement.WebAPI.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "/Users/<USER>/Desktop/A2A/NDUE/NBDO-devel-new-layout-2017/pianificazione_tecnica/budget-api/src/BudgetManagement.WebAPI/BudgetManagement.WebAPI.csproj", "projectName": "BudgetManagement.WebAPI", "projectPath": "/Users/<USER>/Desktop/A2A/NDUE/NBDO-devel-new-layout-2017/pianificazione_tecnica/budget-api/src/BudgetManagement.WebAPI/BudgetManagement.WebAPI.csproj", "packagesPath": "/Users/<USER>/.nuget/packages/", "outputPath": "/Users/<USER>/Desktop/A2A/NDUE/NBDO-devel-new-layout-2017/pianificazione_tecnica/budget-api/src/BudgetManagement.WebAPI/obj/", "projectStyle": "PackageReference", "configFilePaths": ["/Users/<USER>/.nuget/NuGet/NuGet.Config"], "originalTargetFrameworks": ["net9.0"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "projectReferences": {"/Users/<USER>/Desktop/A2A/NDUE/NBDO-devel-new-layout-2017/pianificazione_tecnica/budget-api/src/BudgetManagement.Application/BudgetManagement.Application.csproj": {"projectPath": "/Users/<USER>/Desktop/A2A/NDUE/NBDO-devel-new-layout-2017/pianificazione_tecnica/budget-api/src/BudgetManagement.Application/BudgetManagement.Application.csproj"}, "/Users/<USER>/Desktop/A2A/NDUE/NBDO-devel-new-layout-2017/pianificazione_tecnica/budget-api/src/BudgetManagement.Infrastructure/BudgetManagement.Infrastructure.csproj": {"projectPath": "/Users/<USER>/Desktop/A2A/NDUE/NBDO-devel-new-layout-2017/pianificazione_tecnica/budget-api/src/BudgetManagement.Infrastructure/BudgetManagement.Infrastructure.csproj"}}}}, "warningProperties": {"allWarningsAsErrors": true}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.200"}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "dependencies": {"AspNetCore.HealthChecks.Npgsql": {"target": "Package", "version": "[8.0.2, )"}, "FluentValidation.AspNetCore": {"target": "Package", "version": "[11.3.0, )"}, "Microsoft.AspNetCore.Authentication.JwtBearer": {"target": "Package", "version": "[9.0.0, )"}, "Microsoft.AspNetCore.Authorization": {"target": "Package", "version": "[9.0.0, )"}, "Microsoft.AspNetCore.Mvc.Versioning": {"target": "Package", "version": "[5.1.0, )"}, "Microsoft.AspNetCore.Mvc.Versioning.ApiExplorer": {"target": "Package", "version": "[5.1.0, )"}, "Microsoft.EntityFrameworkCore.Design": {"target": "Package", "version": "[9.0.0, )"}, "Serilog.AspNetCore": {"target": "Package", "version": "[8.0.0, )"}, "Swashbuckle.AspNetCore": {"target": "Package", "version": "[7.0.0, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.AspNetCore.App": {"privateAssets": "none"}, "Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "/usr/local/share/dotnet/sdk/9.0.200/PortableRuntimeIdentifierGraph.json"}}}}}