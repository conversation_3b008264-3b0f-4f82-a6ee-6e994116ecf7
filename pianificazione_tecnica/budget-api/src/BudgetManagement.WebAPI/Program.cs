using BudgetManagement.Application;
using BudgetManagement.Application.Common.Interfaces;
using BudgetManagement.Infrastructure;
using BudgetManagement.WebAPI.Extensions;
using BudgetManagement.WebAPI.Middleware;
using BudgetManagement.WebAPI.Services;
using Serilog;

var builder = WebApplication.CreateBuilder(args);

// Add Serilog
builder.Host.UseSerilog();

// Add services to the container
builder.Services.AddControllers();
builder.Services.AddEndpointsApiExplorer();

// Add application layers
builder.Services.AddApplication();
builder.Services.AddInfrastructure(builder.Configuration);

// Add Web API specific services
builder.Services.AddWebApiServices(builder.Configuration);

// Add current user service
builder.Services.AddScoped<ICurrentUserService, CurrentUserService>();

var app = builder.Build();

// Configure the HTTP request pipeline
if (app.Environment.IsDevelopment())
{
    app.UseSwagger();
    app.UseSwaggerUI(c =>
    {
        c.SwaggerEndpoint("/swagger/v1/swagger.json", "Budget Management API V1");
        c.RoutePrefix = "swagger";
    });
}

// Add middleware
app.UseMiddleware<ExceptionHandlingMiddleware>();
app.UseMiddleware<RequestLoggingMiddleware>();

app.UseHttpsRedirection();

// CORS
app.UseCors("AllowSpecificOrigins");

// Authentication & Authorization
app.UseAuthentication();
app.UseAuthorization();

app.MapControllers();

// Ensure database is created
await app.EnsureDatabaseCreatedAsync();

app.Run();
