using BudgetManagement.Application.Commands.CreateAnnualBudget;
using BudgetManagement.Application.Queries.GetBudget;
using MediatR;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;

namespace BudgetManagement.WebAPI.Controllers;

/// <summary>
/// Controller for budget management operations
/// </summary>
[ApiController]
[Route("api/v1/[controller]")]
[Authorize]
public class BudgetController : ControllerBase
{
    private readonly IMediator _mediator;
    private readonly ILogger<BudgetController> _logger;

    public BudgetController(IMediator mediator, ILogger<BudgetController> logger)
    {
        _mediator = mediator;
        _logger = logger;
    }

    /// <summary>
    /// Creates a new annual budget for a plant
    /// </summary>
    /// <param name="request">The budget creation request</param>
    /// <returns>The created budget information</returns>
    [HttpPost("annual")]
    [ProducesResponseType(typeof(CreateAnnualBudgetResponse), StatusCodes.Status201Created)]
    [ProducesResponseType(typeof(ProblemDetails), StatusCodes.Status400BadRequest)]
    [ProducesResponseType(typeof(ProblemDetails), StatusCodes.Status401Unauthorized)]
    [ProducesResponseType(typeof(ProblemDetails), StatusCodes.Status500InternalServerError)]
    public async Task<IActionResult> CreateAnnualBudget([FromBody] CreateAnnualBudgetRequest request)
    {
        _logger.LogInformation("Creating annual budget for Plant {PlantId}, Year {Year}", 
            request.PlantId, request.Year);

        var command = new CreateAnnualBudgetCommand(
            request.PlantId,
            request.ProductionUnitId,
            request.Year
        );

        var result = await _mediator.Send(command);

        if (result.IsFailure)
        {
            _logger.LogWarning("Failed to create annual budget: {Error}", result.Error);
            return BadRequest(new ProblemDetails 
            { 
                Title = "Budget Creation Failed",
                Detail = result.Error,
                Status = StatusCodes.Status400BadRequest
            });
        }

        _logger.LogInformation("Annual budget created successfully with ID {BudgetId}", 
            result.Value.BudgetId);

        return CreatedAtAction(
            nameof(GetBudget), 
            new { id = result.Value.BudgetId }, 
            result.Value);
    }

    /// <summary>
    /// Gets a budget by its identifier
    /// </summary>
    /// <param name="id">The budget identifier</param>
    /// <returns>The budget information</returns>
    [HttpGet("{id:guid}")]
    [ProducesResponseType(typeof(GetBudgetResponse), StatusCodes.Status200OK)]
    [ProducesResponseType(typeof(ProblemDetails), StatusCodes.Status404NotFound)]
    [ProducesResponseType(typeof(ProblemDetails), StatusCodes.Status401Unauthorized)]
    public async Task<IActionResult> GetBudget(Guid id)
    {
        _logger.LogInformation("Retrieving budget with ID {BudgetId}", id);

        var query = new GetBudgetQuery(id);
        var result = await _mediator.Send(query);

        if (result.IsFailure)
        {
            _logger.LogWarning("Budget not found with ID {BudgetId}", id);
            return NotFound(new ProblemDetails
            {
                Title = "Budget Not Found",
                Detail = result.Error,
                Status = StatusCodes.Status404NotFound
            });
        }

        return Ok(result.Value);
    }

    /// <summary>
    /// Gets the health status of the budget service
    /// </summary>
    /// <returns>Health status</returns>
    [HttpGet("health")]
    [AllowAnonymous]
    [ProducesResponseType(StatusCodes.Status200OK)]
    public IActionResult GetHealth()
    {
        return Ok(new { Status = "Healthy", Timestamp = DateTime.UtcNow });
    }
}

/// <summary>
/// Request model for creating an annual budget
/// </summary>
public record CreateAnnualBudgetRequest(
    Guid PlantId,
    Guid ProductionUnitId,
    int Year
);
