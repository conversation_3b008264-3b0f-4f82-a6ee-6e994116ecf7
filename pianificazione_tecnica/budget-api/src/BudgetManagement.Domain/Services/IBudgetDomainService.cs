using BudgetManagement.Domain.Common;
using BudgetManagement.Domain.ValueObjects;

namespace BudgetManagement.Domain.Services;

/// <summary>
/// Domain service for budget-related operations
/// </summary>
public interface IBudgetDomainService
{
    Task<Result<MaintenancePlanId>> GetMaintenancePlanAsync(PlantId plantId, Year year);
    Task<bool> IsMaintenancePlanValidAsync(PlantId plantId, Year year);
    Task<bool> IsProductionUnitValidAsync(ProductionUnitId productionUnitId, PlantId plantId);
    Task<bool> IsKpiValidForProductionUnitAsync(ProductionUnitId productionUnitId, Year year);
}
