using BudgetManagement.Domain.Common;
using BudgetManagement.Domain.ValueObjects;

namespace BudgetManagement.Domain.Events;

/// <summary>
/// Domain event raised when a budget is created
/// </summary>
public record BudgetCreatedEvent(
    BudgetId BudgetId,
    PlantId PlantId,
    ProductionUnitId ProductionUnitId,
    Year Year) : IDomainEvent
{
    public DateTime OccurredOn { get; } = DateTime.UtcNow;
}
