namespace BudgetManagement.Domain.Enums;

/// <summary>
/// Represents the status of a budget in the approval workflow
/// </summary>
public enum BudgetStatus
{
    /// <summary>
    /// Budget is in draft state and can be edited
    /// </summary>
    Draft = 0,
    
    /// <summary>
    /// Budget has been submitted for approval
    /// </summary>
    Submitted = 1,
    
    /// <summary>
    /// Budget has been approved at plant level
    /// </summary>
    ApprovedPlant = 2,
    
    /// <summary>
    /// Budget is in approval process at headquarters
    /// </summary>
    InApprovalHeadquarters = 3,
    
    /// <summary>
    /// Budget has been approved at headquarters level
    /// </summary>
    ApprovedHeadquarters = 4,
    
    /// <summary>
    /// Budget has been set as reference for the year
    /// </summary>
    Reference = 5,
    
    /// <summary>
    /// Budget has been rejected
    /// </summary>
    Rejected = 6
}
