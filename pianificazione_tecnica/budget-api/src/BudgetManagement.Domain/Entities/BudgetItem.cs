using BudgetManagement.Domain.Common;
using BudgetManagement.Domain.ValueObjects;

namespace BudgetManagement.Domain.Entities;

/// <summary>
/// Entity representing a budget item (KPI value for a specific month)
/// </summary>
public class BudgetItem : Entity<BudgetItemId>
{
    public BudgetId BudgetId { get; private set; }
    public string KpiId { get; private set; }
    public int Month { get; private set; }
    public decimal? ProposedValue { get; private set; }
    public decimal? BudgetValue { get; private set; }
    public string? Notes { get; private set; }
    public DateTime CreatedAt { get; private set; }
    public DateTime? ModifiedAt { get; private set; }

    // Navigation property
    public AnnualBudget Budget { get; private set; } = default!;

    // Private constructor for EF Core
    private BudgetItem() : base()
    {
        BudgetId = default!;
        KpiId = default!;
    }

    private BudgetItem(
        BudgetItemId id,
        BudgetId budgetId,
        string kpiId,
        int month) : base(id)
    {
        BudgetId = budgetId;
        KpiId = kpiId;
        Month = month;
        CreatedAt = DateTime.UtcNow;
    }

    public static BudgetItem Create(
        BudgetId budgetId,
        string kpiId,
        int month)
    {
        if (month < 1 || month > 12)
            throw new ArgumentException("Month must be between 1 and 12", nameof(month));

        if (string.IsNullOrWhiteSpace(kpiId))
            throw new ArgumentException("KPI ID cannot be null or empty", nameof(kpiId));

        return new BudgetItem(BudgetItemId.New(), budgetId, kpiId, month);
    }

    public void UpdateProposedValue(decimal? value)
    {
        if (value.HasValue && value < 0)
            throw new ArgumentException("Proposed value cannot be negative", nameof(value));

        ProposedValue = value;
        ModifiedAt = DateTime.UtcNow;
    }

    public void UpdateBudgetValue(decimal? value)
    {
        if (value.HasValue && value < 0)
            throw new ArgumentException("Budget value cannot be negative", nameof(value));

        BudgetValue = value;
        ModifiedAt = DateTime.UtcNow;
    }

    public void UpdateNotes(string? notes)
    {
        Notes = notes;
        ModifiedAt = DateTime.UtcNow;
    }
}

/// <summary>
/// Value object representing a budget item identifier
/// </summary>
public record BudgetItemId(Guid Value)
{
    public static BudgetItemId New() => new(Guid.NewGuid());
    public static BudgetItemId From(Guid value) => new(value);
    
    public override string ToString() => Value.ToString();
    
    public static implicit operator Guid(BudgetItemId budgetItemId) => budgetItemId.Value;
    public static implicit operator BudgetItemId(Guid value) => new(value);
}
