using BudgetManagement.Domain.Common;
using BudgetManagement.Domain.Enums;
using BudgetManagement.Domain.Events;
using BudgetManagement.Domain.ValueObjects;

namespace BudgetManagement.Domain.Entities;

/// <summary>
/// Aggregate root representing an annual budget for a plant
/// </summary>
public class AnnualBudget : AggregateRoot<BudgetId>
{
    private readonly List<BudgetItem> _items = new();

    public PlantId PlantId { get; private set; }
    public ProductionUnitId ProductionUnitId { get; private set; }
    public Year Year { get; private set; }
    public BudgetStatus Status { get; private set; }
    public int Version { get; private set; }
    public UserId CreatedBy { get; private set; }
    public DateTime CreatedAt { get; private set; }
    public DateTime? ModifiedAt { get; private set; }
    public MaintenancePlanId? MaintenancePlanId { get; private set; }
    public DateTime? ApprovedAtPlantDate { get; private set; }
    public UserId? ApprovedAtPlantBy { get; private set; }
    public DateTime? ApprovedAtHeadquartersDate { get; private set; }
    public UserId? ApprovedAtHeadquartersBy { get; private set; }

    public IReadOnlyList<BudgetItem> Items => _items.AsReadOnly();

    // Private constructor for EF Core
    private AnnualBudget() : base()
    {
        PlantId = default!;
        ProductionUnitId = default!;
        Year = default!;
        CreatedBy = default!;
    }

    private AnnualBudget(
        BudgetId id,
        PlantId plantId,
        ProductionUnitId productionUnitId,
        Year year,
        UserId createdBy) : base(id)
    {
        PlantId = plantId;
        ProductionUnitId = productionUnitId;
        Year = year;
        Status = BudgetStatus.Draft;
        Version = 1;
        CreatedBy = createdBy;
        CreatedAt = DateTime.UtcNow;

        AddDomainEvent(new BudgetCreatedEvent(Id, PlantId, ProductionUnitId, Year));
    }

    public static AnnualBudget Create(
        PlantId plantId,
        ProductionUnitId productionUnitId,
        Year year,
        UserId createdBy)
    {
        return new AnnualBudget(BudgetId.New(), plantId, productionUnitId, year, createdBy);
    }

    public Result Submit(UserId userId)
    {
        if (Status != BudgetStatus.Draft)
            return Result.Failure("Budget can only be submitted from Draft status");

        if (!_items.Any())
            return Result.Failure("Budget must have at least one item to be submitted");

        Status = BudgetStatus.Submitted;
        ModifiedAt = DateTime.UtcNow;

        AddDomainEvent(new BudgetSubmittedEvent(Id, userId));
        return Result.Success();
    }

    public Result ApproveAtPlant(UserId userId)
    {
        if (Status != BudgetStatus.Submitted)
            return Result.Failure("Budget can only be approved at plant level from Submitted status");

        Status = BudgetStatus.ApprovedPlant;
        ApprovedAtPlantDate = DateTime.UtcNow;
        ApprovedAtPlantBy = userId;
        ModifiedAt = DateTime.UtcNow;

        AddDomainEvent(new BudgetApprovedAtPlantEvent(Id, userId));
        return Result.Success();
    }

    public Result ApproveAtHeadquarters(UserId userId)
    {
        if (Status != BudgetStatus.ApprovedPlant && Status != BudgetStatus.InApprovalHeadquarters)
            return Result.Failure("Budget can only be approved at headquarters from ApprovedPlant or InApprovalHeadquarters status");

        Status = BudgetStatus.ApprovedHeadquarters;
        ApprovedAtHeadquartersDate = DateTime.UtcNow;
        ApprovedAtHeadquartersBy = userId;
        ModifiedAt = DateTime.UtcNow;

        AddDomainEvent(new BudgetApprovedAtHeadquartersEvent(Id, userId));
        return Result.Success();
    }

    public Result Reject(UserId userId, string reason)
    {
        if (Status == BudgetStatus.Draft || Status == BudgetStatus.Reference)
            return Result.Failure("Budget cannot be rejected from current status");

        Status = BudgetStatus.Rejected;
        ModifiedAt = DateTime.UtcNow;

        AddDomainEvent(new BudgetRejectedEvent(Id, userId, reason));
        return Result.Success();
    }

    public Result SetAsReference(UserId userId)
    {
        if (Status != BudgetStatus.ApprovedHeadquarters)
            return Result.Failure("Only approved budgets can be set as reference");

        Status = BudgetStatus.Reference;
        ModifiedAt = DateTime.UtcNow;

        AddDomainEvent(new BudgetSetAsReferenceEvent(Id, userId));
        return Result.Success();
    }

    public void SetMaintenancePlan(MaintenancePlanId maintenancePlanId)
    {
        MaintenancePlanId = maintenancePlanId;
        ModifiedAt = DateTime.UtcNow;
    }

    public void AddItem(BudgetItem item)
    {
        _items.Add(item);
        ModifiedAt = DateTime.UtcNow;
    }

    public void RemoveItem(BudgetItem item)
    {
        _items.Remove(item);
        ModifiedAt = DateTime.UtcNow;
    }

    public void UpdateVersion()
    {
        Version++;
        ModifiedAt = DateTime.UtcNow;
    }
}
