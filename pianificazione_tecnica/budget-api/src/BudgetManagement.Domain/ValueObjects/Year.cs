namespace BudgetManagement.Domain.ValueObjects;

/// <summary>
/// Value object representing a year
/// </summary>
public record Year(int Value)
{
    public static Year From(int value)
    {
        if (value < 2020 || value > 2050)
            throw new ArgumentException("Year must be between 2020 and 2050", nameof(value));
        
        return new Year(value);
    }
    
    public static Year Current => new(DateTime.Now.Year);
    public static Year Next => new(DateTime.Now.Year + 1);
    
    public override string ToString() => Value.ToString();
    
    public static implicit operator int(Year year) => year.Value;
    public static implicit operator Year(int value) => From(value);
}
