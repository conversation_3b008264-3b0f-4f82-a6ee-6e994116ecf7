namespace BudgetManagement.Domain.ValueObjects;

/// <summary>
/// Value object representing a maintenance plan identifier
/// </summary>
public record MaintenancePlanId(Guid Value)
{
    public static MaintenancePlanId New() => new(Guid.NewGuid());
    public static MaintenancePlanId From(Guid value) => new(value);
    
    public override string ToString() => Value.ToString();
    
    public static implicit operator Guid(MaintenancePlanId maintenancePlanId) => maintenancePlanId.Value;
    public static implicit operator MaintenancePlanId(Guid value) => new(value);
}
