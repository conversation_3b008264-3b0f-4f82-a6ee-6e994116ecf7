namespace BudgetManagement.Domain.ValueObjects;

/// <summary>
/// Value object representing a plant identifier
/// </summary>
public record PlantId(Guid Value)
{
    public static PlantId New() => new(Guid.NewGuid());
    public static PlantId From(Guid value) => new(value);
    
    public override string ToString() => Value.ToString();
    
    public static implicit operator Guid(PlantId plantId) => plantId.Value;
    public static implicit operator PlantId(Guid value) => new(value);
}
