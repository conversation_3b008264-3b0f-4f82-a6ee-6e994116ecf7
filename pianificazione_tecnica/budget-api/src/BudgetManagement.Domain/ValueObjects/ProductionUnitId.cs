namespace BudgetManagement.Domain.ValueObjects;

/// <summary>
/// Value object representing a production unit identifier
/// </summary>
public record ProductionUnitId(Guid Value)
{
    public static ProductionUnitId New() => new(Guid.NewGuid());
    public static ProductionUnitId From(Guid value) => new(value);
    
    public override string ToString() => Value.ToString();
    
    public static implicit operator Guid(ProductionUnitId productionUnitId) => productionUnitId.Value;
    public static implicit operator ProductionUnitId(Guid value) => new(value);
}
