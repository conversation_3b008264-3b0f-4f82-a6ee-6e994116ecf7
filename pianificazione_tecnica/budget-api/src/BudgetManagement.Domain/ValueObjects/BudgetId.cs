namespace BudgetManagement.Domain.ValueObjects;

/// <summary>
/// Value object representing a budget identifier
/// </summary>
public record BudgetId(Guid Value)
{
    public static BudgetId New() => new(Guid.NewGuid());
    public static BudgetId From(Guid value) => new(value);
    
    public override string ToString() => Value.ToString();
    
    public static implicit operator Guid(BudgetId budgetId) => budgetId.Value;
    public static implicit operator BudgetId(Guid value) => new(value);
}
