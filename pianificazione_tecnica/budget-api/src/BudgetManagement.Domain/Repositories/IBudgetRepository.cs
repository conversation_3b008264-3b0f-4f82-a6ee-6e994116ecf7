using BudgetManagement.Domain.Entities;
using BudgetManagement.Domain.Enums;
using BudgetManagement.Domain.ValueObjects;

namespace BudgetManagement.Domain.Repositories;

/// <summary>
/// Repository interface for budget operations
/// </summary>
public interface IBudgetRepository
{
    Task<AnnualBudget?> GetByIdAsync(BudgetId id, CancellationToken cancellationToken = default);
    
    Task<AnnualBudget?> GetByPlantAndYearAsync(
        PlantId plantId, 
        Year year, 
        CancellationToken cancellationToken = default);
    
    Task<bool> ExistsAsync(
        PlantId plantId, 
        ProductionUnitId productionUnitId, 
        Year year, 
        CancellationToken cancellationToken = default);
    
    Task<IEnumerable<AnnualBudget>> GetAllAsync(CancellationToken cancellationToken = default);
    
    Task<IEnumerable<AnnualBudget>> GetByFiltersAsync(
        Year? year = null,
        BudgetStatus? status = null,
        CancellationToken cancellationToken = default);
    
    Task AddAsync(AnnualBudget budget, CancellationToken cancellationToken = default);
    
    void Update(AnnualBudget budget);
    
    void Remove(AnnualBudget budget);
}
