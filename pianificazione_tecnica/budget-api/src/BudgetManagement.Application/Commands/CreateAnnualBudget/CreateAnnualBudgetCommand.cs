using BudgetManagement.Domain.Common;
using BudgetManagement.Domain.ValueObjects;
using MediatR;

namespace BudgetManagement.Application.Commands.CreateAnnualBudget;

/// <summary>
/// Command to create a new annual budget
/// </summary>
public record CreateAnnualBudgetCommand(
    Guid PlantId,
    Guid ProductionUnitId,
    int Year) : IRequest<Result<CreateAnnualBudgetResponse>>;

/// <summary>
/// Response for create annual budget command
/// </summary>
public record CreateAnnualBudgetResponse(
    Guid BudgetId,
    string Status,
    string? MaintenancePlanInfo = null,
    string? Message = null);
