using BudgetManagement.Application.Common.Interfaces;
using BudgetManagement.Domain.Common;
using BudgetManagement.Domain.Entities;
using BudgetManagement.Domain.Repositories;
using BudgetManagement.Domain.Services;
using BudgetManagement.Domain.ValueObjects;
using MediatR;

namespace BudgetManagement.Application.Commands.CreateAnnualBudget;

/// <summary>
/// Handler for create annual budget command
/// </summary>
public class CreateAnnualBudgetCommandHandler : IRequestHandler<CreateAnnualBudgetCommand, Result<CreateAnnualBudgetResponse>>
{
    private readonly IBudgetRepository _budgetRepository;
    private readonly IBudgetDomainService _budgetDomainService;
    private readonly ICurrentUserService _currentUserService;
    private readonly IUnitOfWork _unitOfWork;

    public CreateAnnualBudgetCommandHandler(
        IBudgetRepository budgetRepository,
        IBudgetDomainService budgetDomainService,
        ICurrentUserService currentUserService,
        IUnitOfWork unitOfWork)
    {
        _budgetRepository = budgetRepository;
        _budgetDomainService = budgetDomainService;
        _currentUserService = currentUserService;
        _unitOfWork = unitOfWork;
    }

    public async Task<Result<CreateAnnualBudgetResponse>> Handle(
        CreateAnnualBudgetCommand request, 
        CancellationToken cancellationToken)
    {
        // Validate user is authenticated
        if (_currentUserService.UserId is null)
            return Result.Failure<CreateAnnualBudgetResponse>("User must be authenticated");

        var plantId = PlantId.From(request.PlantId);
        var productionUnitId = ProductionUnitId.From(request.ProductionUnitId);
        var year = Year.From(request.Year);
        var userId = _currentUserService.UserId;

        // Check if budget already exists
        var existingBudget = await _budgetRepository.ExistsAsync(plantId, productionUnitId, year, cancellationToken);
        if (existingBudget)
            return Result.Failure<CreateAnnualBudgetResponse>(
                $"Budget already exists for Plant {plantId}, Production Unit {productionUnitId}, Year {year}");

        // Validate production unit belongs to plant
        var isValidProductionUnit = await _budgetDomainService.IsProductionUnitValidAsync(productionUnitId, plantId);
        if (!isValidProductionUnit)
            return Result.Failure<CreateAnnualBudgetResponse>("Production unit does not belong to the specified plant");

        // Create the budget
        var budget = AnnualBudget.Create(plantId, productionUnitId, year, userId);

        // Try to get maintenance plan
        var maintenancePlanResult = await _budgetDomainService.GetMaintenancePlanAsync(plantId, year);
        string? maintenancePlanInfo = null;
        
        if (maintenancePlanResult.IsSuccess)
        {
            budget.SetMaintenancePlan(maintenancePlanResult.Value);
            maintenancePlanInfo = $"Maintenance plan {maintenancePlanResult.Value} associated";
        }

        // Save the budget
        await _budgetRepository.AddAsync(budget, cancellationToken);
        await _unitOfWork.SaveChangesAsync(cancellationToken);

        var response = new CreateAnnualBudgetResponse(
            budget.Id.Value,
            budget.Status.ToString(),
            maintenancePlanInfo,
            "Budget created successfully");

        return Result.Success(response);
    }
}
