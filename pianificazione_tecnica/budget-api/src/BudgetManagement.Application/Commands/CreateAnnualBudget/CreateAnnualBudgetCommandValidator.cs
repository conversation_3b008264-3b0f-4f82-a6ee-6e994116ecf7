using FluentValidation;

namespace BudgetManagement.Application.Commands.CreateAnnualBudget;

/// <summary>
/// Validator for create annual budget command
/// </summary>
public class CreateAnnualBudgetCommandValidator : AbstractValidator<CreateAnnualBudgetCommand>
{
    public CreateAnnualBudgetCommandValidator()
    {
        RuleFor(x => x.PlantId)
            .NotEmpty()
            .WithMessage("Plant ID is required");

        RuleFor(x => x.ProductionUnitId)
            .NotEmpty()
            .WithMessage("Production Unit ID is required");

        RuleFor(x => x.Year)
            .GreaterThanOrEqualTo(2020)
            .WithMessage("Year must be 2020 or later")
            .LessThanOrEqualTo(2050)
            .WithMessage("Year must be 2050 or earlier");
    }
}
