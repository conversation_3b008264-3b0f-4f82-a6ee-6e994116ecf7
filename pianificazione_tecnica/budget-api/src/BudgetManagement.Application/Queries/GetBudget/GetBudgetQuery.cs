using BudgetManagement.Domain.Common;
using BudgetManagement.Domain.Enums;
using MediatR;

namespace BudgetManagement.Application.Queries.GetBudget;

/// <summary>
/// Query to get a budget by ID
/// </summary>
public record GetBudgetQuery(Guid BudgetId) : IRequest<Result<GetBudgetResponse>>;

/// <summary>
/// Response for get budget query
/// </summary>
public record GetBudgetResponse(
    Guid Id,
    Guid PlantId,
    Guid ProductionUnitId,
    int Year,
    BudgetStatus Status,
    int Version,
    string CreatedBy,
    DateTime CreatedAt,
    DateTime? ModifiedAt,
    Guid? MaintenancePlanId,
    DateTime? ApprovedAtPlantDate,
    string? ApprovedAtPlantBy,
    DateTime? ApprovedAtHeadquartersDate,
    string? ApprovedAtHeadquartersBy,
    IEnumerable<BudgetItemResponse> Items);

/// <summary>
/// Budget item response
/// </summary>
public record BudgetItemResponse(
    Guid Id,
    string KpiId,
    int Month,
    decimal? ProposedValue,
    decimal? BudgetValue,
    string? Notes,
    DateTime CreatedAt,
    DateTime? ModifiedAt);
