using BudgetManagement.Domain.Common;
using BudgetManagement.Domain.Repositories;
using BudgetManagement.Domain.ValueObjects;
using MediatR;

namespace BudgetManagement.Application.Queries.GetBudget;

/// <summary>
/// Handler for get budget query
/// </summary>
public class GetBudgetQueryHandler : IRequestHandler<GetBudgetQuery, Result<GetBudgetResponse>>
{
    private readonly IBudgetRepository _budgetRepository;

    public GetBudgetQueryHandler(IBudgetRepository budgetRepository)
    {
        _budgetRepository = budgetRepository;
    }

    public async Task<Result<GetBudgetResponse>> Handle(
        GetBudgetQuery request, 
        CancellationToken cancellationToken)
    {
        var budgetId = BudgetId.From(request.BudgetId);
        var budget = await _budgetRepository.GetByIdAsync(budgetId, cancellationToken);

        if (budget is null)
            return Result.Failure<GetBudgetResponse>($"Budget with ID {request.BudgetId} not found");

        var response = new GetBudgetResponse(
            budget.Id.Value,
            budget.PlantId.Value,
            budget.ProductionUnitId.Value,
            budget.Year.Value,
            budget.Status,
            budget.Version,
            budget.CreatedBy.Value.ToString(),
            budget.CreatedAt,
            budget.ModifiedAt,
            budget.MaintenancePlanId?.Value,
            budget.ApprovedAtPlantDate,
            budget.ApprovedAtPlantBy?.Value.ToString(),
            budget.ApprovedAtHeadquartersDate,
            budget.ApprovedAtHeadquartersBy?.Value.ToString(),
            budget.Items.Select(item => new BudgetItemResponse(
                item.Id.Value,
                item.KpiId,
                item.Month,
                item.ProposedValue,
                item.BudgetValue,
                item.Notes,
                item.CreatedAt,
                item.ModifiedAt)));

        return Result.Success(response);
    }
}
