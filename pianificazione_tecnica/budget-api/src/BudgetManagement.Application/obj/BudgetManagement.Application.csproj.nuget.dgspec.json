{"format": 1, "restore": {"/Users/<USER>/Desktop/A2A/NDUE/NBDO-devel-new-layout-2017/pianificazione_tecnica/budget-api/src/BudgetManagement.Application/BudgetManagement.Application.csproj": {}}, "projects": {"/Users/<USER>/Desktop/A2A/NDUE/NBDO-devel-new-layout-2017/pianificazione_tecnica/budget-api/src/BudgetManagement.Application/BudgetManagement.Application.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "/Users/<USER>/Desktop/A2A/NDUE/NBDO-devel-new-layout-2017/pianificazione_tecnica/budget-api/src/BudgetManagement.Application/BudgetManagement.Application.csproj", "projectName": "BudgetManagement.Application", "projectPath": "/Users/<USER>/Desktop/A2A/NDUE/NBDO-devel-new-layout-2017/pianificazione_tecnica/budget-api/src/BudgetManagement.Application/BudgetManagement.Application.csproj", "packagesPath": "/Users/<USER>/.nuget/packages/", "outputPath": "/Users/<USER>/Desktop/A2A/NDUE/NBDO-devel-new-layout-2017/pianificazione_tecnica/budget-api/src/BudgetManagement.Application/obj/", "projectStyle": "PackageReference", "configFilePaths": ["/Users/<USER>/.nuget/NuGet/NuGet.Config"], "originalTargetFrameworks": ["net9.0"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "projectReferences": {"/Users/<USER>/Desktop/A2A/NDUE/NBDO-devel-new-layout-2017/pianificazione_tecnica/budget-api/src/BudgetManagement.Domain/BudgetManagement.Domain.csproj": {"projectPath": "/Users/<USER>/Desktop/A2A/NDUE/NBDO-devel-new-layout-2017/pianificazione_tecnica/budget-api/src/BudgetManagement.Domain/BudgetManagement.Domain.csproj"}}}}, "warningProperties": {"allWarningsAsErrors": true}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.200"}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "dependencies": {"AutoMapper": {"target": "Package", "version": "[13.0.1, )"}, "FluentValidation": {"target": "Package", "version": "[11.9.0, )"}, "FluentValidation.DependencyInjectionExtensions": {"target": "Package", "version": "[11.9.0, )"}, "MediatR": {"target": "Package", "version": "[12.2.0, )"}, "Microsoft.Extensions.Logging.Abstractions": {"target": "Package", "version": "[9.0.0, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "/usr/local/share/dotnet/sdk/9.0.200/PortableRuntimeIdentifierGraph.json"}}}, "/Users/<USER>/Desktop/A2A/NDUE/NBDO-devel-new-layout-2017/pianificazione_tecnica/budget-api/src/BudgetManagement.Domain/BudgetManagement.Domain.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "/Users/<USER>/Desktop/A2A/NDUE/NBDO-devel-new-layout-2017/pianificazione_tecnica/budget-api/src/BudgetManagement.Domain/BudgetManagement.Domain.csproj", "projectName": "BudgetManagement.Domain", "projectPath": "/Users/<USER>/Desktop/A2A/NDUE/NBDO-devel-new-layout-2017/pianificazione_tecnica/budget-api/src/BudgetManagement.Domain/BudgetManagement.Domain.csproj", "packagesPath": "/Users/<USER>/.nuget/packages/", "outputPath": "/Users/<USER>/Desktop/A2A/NDUE/NBDO-devel-new-layout-2017/pianificazione_tecnica/budget-api/src/BudgetManagement.Domain/obj/", "projectStyle": "PackageReference", "configFilePaths": ["/Users/<USER>/.nuget/NuGet/NuGet.Config"], "originalTargetFrameworks": ["net9.0"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "projectReferences": {}}}, "warningProperties": {"allWarningsAsErrors": true}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.200"}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "dependencies": {"MediatR": {"target": "Package", "version": "[12.2.0, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "/usr/local/share/dotnet/sdk/9.0.200/PortableRuntimeIdentifierGraph.json"}}}}}