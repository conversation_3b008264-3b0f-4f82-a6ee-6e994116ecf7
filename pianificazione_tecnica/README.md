# Budget Management System - Modern Micro-Frontend Architecture

## 🏗️ Architettura Moderna con Micro-Frontend

Questo progetto rappresenta la modernizzazione del sistema legacy di gestione budget NBDO, trasformato in un'architettura moderna basata su:

- **Clean Architecture** con separazione dei layer
- **Domain Driven Design (DDD)** per la logica di business
- **CQRS + MediatR** per la gestione dei comandi e query
- **Entity Framework Core** con PostgreSQL per l'accesso ai dati
- **.NET 9 Web API** per i servizi REST
- **React 18 + TypeScript + Vite** per micro-frontend
- **Module Federation** per architettura modulare
- **Azure AD / EntraID** per autenticazione

## 📁 Struttura del Progetto

```
pianificazione_tecnica/
├── 📁 portal/                           # Shell application (authentication + routing)
├── 📁 auth-api/                         # Authentication service
├── 📁 budget-api/                       # Budget management service API
├── 📁 budget-fe/                        # Budget micro-frontend application
├── 📁 shared/                           # Shared components and utilities
│   ├── 📁 ui-components/               # Reusable React components
│   ├── 📁 api-contracts/               # TypeScript type definitions
│   └── 📁 utilities/                   # Common utilities
├── 📁 infrastructure/                   # Deployment configurations
├── 📁 tests/                           # Test projects
│   ├── 📁 budget-api.tests/
│   └── 📁 integration.tests/
├── 📄 config.env.template              # Environment configuration template
├── 📄 start-local.sh                   # Development startup script
├── 📄 Dockerfile.frontend              # Generic frontend Dockerfile
├── 📄 nginx.conf                       # Nginx configuration
└── 📄 README.md                        # Project documentation
```

## 🚀 Tecnologie Utilizzate

### Backend
- .NET 9
- ASP.NET Core Web API
- Entity Framework Core
- PostgreSQL
- MediatR
- FluentValidation
- Serilog
- AutoMapper

### Frontend
- React 18
- TypeScript
- Vite
- Webpack Module Federation
- Ant Design
- React Query
- Axios

### Authentication
- Azure AD / EntraID
- JWT Tokens

### Database
- PostgreSQL
- Redis (Cache)

### DevOps
- Docker with multi-stage builds
- Docker Compose
- GitHub Actions
- Nginx

## 🏃‍♂️ Quick Start

1. **Clone del repository**
```bash
git clone <repository-url>
cd pianificazione_tecnica
```

2. **Avvio con Docker Compose**
```bash
docker-compose up -d
```

3. **Accesso all'applicazione**
- Portal (Shell): http://localhost:3000
- Budget Frontend: http://localhost:3001
- Budget API: http://localhost:5000
- Auth API: http://localhost:5001
- Swagger: http://localhost:5000/swagger

## 📖 Documentazione

- [Architettura](docs/architecture/README.md)
- [API Documentation](docs/api/README.md)
- [Frontend Guide](src/BudgetManagement.WebApp/README.md)

## 🧪 Testing

```bash
# Unit Tests
dotnet test tests/BudgetManagement.Domain.Tests/
dotnet test tests/BudgetManagement.Application.Tests/

# Integration Tests
dotnet test tests/BudgetManagement.Integration.Tests/
```

## 📝 Migrazione dal Sistema Legacy

Questo sistema sostituisce il modulo legacy ASP.NET WebForms per la gestione del budget annuale di impianto, mantenendo la stessa logica di business ma con:

- Architettura moderna e scalabile
- API REST per integrazione
- Frontend reattivo e user-friendly
- Testing automatizzato
- Deployment containerizzato

## 🤝 Contributi

Per contribuire al progetto, seguire le [linee guida](CONTRIBUTING.md).

## 📄 Licenza

[MIT License](LICENSE)
