# Budget Management System - Modern Architecture

## 🏗️ Architettura Clean Architecture + DDD

Questo progetto rappresenta la modernizzazione del sistema legacy di gestione budget NBDO, trasformato in un'architettura moderna basata su:

- **Clean Architecture** con separazione dei layer
- **Domain Driven Design (DDD)** per la logica di business
- **CQRS + MediatR** per la gestione dei comandi e query
- **Entity Framework Core** per l'accesso ai dati
- **ASP.NET Core Web API** per i servizi REST
- **React + TypeScript** per il frontend moderno

## 📁 Struttura del Progetto

```
pianificazione_tecnica/
├── src/
│   ├── BudgetManagement.Domain/          # Core Business Logic
│   ├── BudgetManagement.Application/     # Use Cases & Services
│   ├── BudgetManagement.Infrastructure/  # Data Access & External
│   ├── BudgetManagement.WebAPI/          # REST API
│   └── BudgetManagement.WebApp/          # React SPA
├── tests/
│   ├── BudgetManagement.Domain.Tests/
│   ├── BudgetManagement.Application.Tests/
│   └── BudgetManagement.Integration.Tests/
└── docs/
    ├── architecture/
    └── api/
```

## 🚀 Tecnologie Utilizzate

### Backend
- .NET 8
- ASP.NET Core Web API
- Entity Framework Core
- MediatR
- FluentValidation
- Serilog
- AutoMapper

### Frontend
- React 18
- TypeScript
- Ant Design
- React Query
- Axios
- Vite

### Database
- SQL Server
- Redis (Cache)

### DevOps
- Docker
- Docker Compose
- GitHub Actions

## 🏃‍♂️ Quick Start

1. **Clone del repository**
```bash
git clone <repository-url>
cd pianificazione_tecnica
```

2. **Avvio con Docker Compose**
```bash
docker-compose up -d
```

3. **Accesso all'applicazione**
- Frontend: http://localhost:3000
- API: http://localhost:5000
- Swagger: http://localhost:5000/swagger

## 📖 Documentazione

- [Architettura](docs/architecture/README.md)
- [API Documentation](docs/api/README.md)
- [Frontend Guide](src/BudgetManagement.WebApp/README.md)

## 🧪 Testing

```bash
# Unit Tests
dotnet test tests/BudgetManagement.Domain.Tests/
dotnet test tests/BudgetManagement.Application.Tests/

# Integration Tests
dotnet test tests/BudgetManagement.Integration.Tests/
```

## 📝 Migrazione dal Sistema Legacy

Questo sistema sostituisce il modulo legacy ASP.NET WebForms per la gestione del budget annuale di impianto, mantenendo la stessa logica di business ma con:

- Architettura moderna e scalabile
- API REST per integrazione
- Frontend reattivo e user-friendly
- Testing automatizzato
- Deployment containerizzato

## 🤝 Contributi

Per contribuire al progetto, seguire le [linee guida](CONTRIBUTING.md).

## 📄 Licenza

[MIT License](LICENSE)
