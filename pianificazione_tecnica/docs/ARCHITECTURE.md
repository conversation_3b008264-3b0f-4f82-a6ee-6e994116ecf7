# Budget Management System - Architecture Documentation

## 🏗️ Overview

Il Budget Management System è una modernizzazione del sistema legacy NBDO per la gestione del budget annuale di impianti, implementato con architettura micro-frontend e microservizi.

## 🎯 Obiettivi Architetturali

- **Modernizzazione**: Migrazione da ASP.NET WebForms a .NET 9 + React 18
- **Scalabilità**: Architettura modulare con micro-frontend
- **Manutenibilità**: Clean Architecture con Domain Driven Design
- **Performance**: PostgreSQL, Redis caching, async/await
- **Sicurezza**: Azure AD/EntraID, JWT tokens, HTTPS

## 📐 Architettura del Sistema

### High-Level Architecture

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│     Portal      │    │   Budget FE     │    │   Other MFE     │
│  (Shell App)    │    │ (Micro-Frontend)│    │ (Future Modules)│
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
         └───────────────────────┼───────────────────────┘
                                 │
                    ┌─────────────────┐
                    │     Nginx       │
                    │  (API Gateway)  │
                    └─────────────────┘
                                 │
         ┌───────────────────────┼───────────────────────┐
         │                       │                       │
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Budget API    │    │    Auth API     │    │   Other APIs    │
│   (.NET 9)      │    │   (.NET 9)      │    │  (Future APIs)  │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
         └───────────────────────┼───────────────────────┘
                                 │
                    ┌─────────────────┐
                    │   PostgreSQL    │
                    │   + Redis       │
                    └─────────────────┘
```

### Clean Architecture Layers

```
┌─────────────────────────────────────────────────────────────┐
│                    Presentation Layer                       │
│  ┌─────────────────┐  ┌─────────────────┐  ┌──────────────┐ │
│  │   Portal SPA    │  │  Budget MFE     │  │ Controllers  │ │
│  │   (React 18)    │  │  (React 18)     │  │ (ASP.NET)    │ │
│  └─────────────────┘  └─────────────────┘  └──────────────┘ │
└─────────────────────────────────────────────────────────────┘
┌─────────────────────────────────────────────────────────────┐
│                    Application Layer                        │
│  ┌─────────────────┐  ┌─────────────────┐  ┌──────────────┐ │
│  │   Commands      │  │    Queries      │  │  Validators  │ │
│  │   (CQRS)        │  │   (CQRS)        │  │ (FluentVal.) │ │
│  └─────────────────┘  └─────────────────┘  └──────────────┘ │
└─────────────────────────────────────────────────────────────┘
┌─────────────────────────────────────────────────────────────┐
│                     Domain Layer                            │
│  ┌─────────────────┐  ┌─────────────────┐  ┌──────────────┐ │
│  │   Entities      │  │  Value Objects  │  │    Events    │ │
│  │ (Aggregates)    │  │   (DDD)         │  │   (Domain)   │ │
│  └─────────────────┘  └─────────────────┘  └──────────────┘ │
└─────────────────────────────────────────────────────────────┘
┌─────────────────────────────────────────────────────────────┐
│                  Infrastructure Layer                       │
│  ┌─────────────────┐  ┌─────────────────┐  ┌──────────────┐ │
│  │  Repositories   │  │   EF Core       │  │   Services   │ │
│  │  (Data Access)  │  │ (PostgreSQL)    │  │  (External)  │ │
│  └─────────────────┘  └─────────────────┘  └──────────────┘ │
└─────────────────────────────────────────────────────────────┘
```

## 🔧 Technology Stack

### Backend
- **.NET 9**: Framework principale
- **ASP.NET Core Web API**: REST APIs
- **Entity Framework Core**: ORM per PostgreSQL
- **MediatR**: CQRS pattern implementation
- **FluentValidation**: Input validation
- **AutoMapper**: Object mapping
- **Serilog**: Structured logging

### Frontend
- **React 18**: UI library
- **TypeScript**: Type safety
- **Vite**: Build tool e dev server
- **Webpack Module Federation**: Micro-frontend architecture
- **Ant Design**: UI component library
- **React Query**: Server state management
- **React Router**: Client-side routing

### Database & Caching
- **PostgreSQL 16**: Primary database
- **Redis**: Caching layer
- **Entity Framework Migrations**: Schema management

### DevOps & Infrastructure
- **Docker**: Containerization
- **Docker Compose**: Local development
- **Nginx**: Reverse proxy e static file serving
- **GitHub Actions**: CI/CD (future)

## 🏛️ Domain Model

### Core Entities

```csharp
// Budget Aggregate Root
public class AnnualBudget : AggregateRoot<BudgetId>
{
    public PlantId PlantId { get; private set; }
    public ProductionUnitId ProductionUnitId { get; private set; }
    public Year Year { get; private set; }
    public BudgetStatus Status { get; private set; }
    public IReadOnlyList<BudgetItem> Items { get; }
    
    // Business methods
    public Result Submit(UserId userId);
    public Result ApproveAtPlant(UserId userId);
    public Result ApproveAtHeadquarters(UserId userId);
}

// Value Objects
public record BudgetId(Guid Value) : IStronglyTypedId<Guid>;
public record PlantId(Guid Value) : IStronglyTypedId<Guid>;
public record Year(int Value);

// Domain Events
public record BudgetCreatedEvent(BudgetId BudgetId, PlantId PlantId, Year Year);
```

### Business Rules

1. **Budget Creation**: Un budget può essere creato solo per combinazioni uniche di Impianto + UP + Anno
2. **Workflow States**: Draft → Submitted → ApprovedPlant → ApprovedHeadquarters → Reference
3. **Authorization**: Solo utenti con ruoli appropriati possono approvare budget
4. **Validation**: Anno deve essere tra 2020-2050, valori numerici devono essere positivi

## 🔄 Data Flow

### Create Budget Flow

```
1. User Input (Portal) 
   ↓
2. CreateBudgetForm (Budget MFE)
   ↓
3. POST /api/v1/budget/annual (Budget API)
   ↓
4. CreateAnnualBudgetCommand (Application)
   ↓
5. AnnualBudget.Create() (Domain)
   ↓
6. BudgetRepository.AddAsync() (Infrastructure)
   ↓
7. PostgreSQL Database
```

### Module Federation Flow

```
1. Portal loads at localhost:3000
   ↓
2. Portal fetches remoteEntry.js from Budget MFE
   ↓
3. Budget MFE components loaded dynamically
   ↓
4. Shared dependencies (React, Antd) resolved
   ↓
5. Budget components rendered in Portal shell
```

## 🔐 Security Architecture

### Authentication Flow

```
1. User login → Auth API
2. JWT token generated
3. Token stored in localStorage
4. Token sent in Authorization header
5. Token validated by each API
6. User context available in requests
```

### Authorization

- **Role-based**: Administrator, BudgetManager, User
- **Permission-based**: CreateBudget, ApproveBudget, ViewReports
- **Resource-based**: Users can only access their plant's budgets

## 📊 Performance Considerations

### Caching Strategy
- **Redis**: API responses, user sessions
- **Browser**: Static assets, API responses (React Query)
- **CDN**: Static assets (production)

### Database Optimization
- **Indexes**: Composite indexes on (PlantId, Year, Status)
- **Pagination**: All list endpoints support pagination
- **Lazy Loading**: EF Core navigation properties

### Frontend Optimization
- **Code Splitting**: Module Federation + React.lazy
- **Bundle Optimization**: Vite tree-shaking
- **Image Optimization**: WebP format, lazy loading

## 🚀 Deployment Architecture

### Development
```
Docker Compose:
- PostgreSQL container
- Redis container  
- Budget API container
- Auth API container
- Budget Frontend container
- Portal container
- Nginx container
```

### Production (Future)
```
Kubernetes:
- Database: Managed PostgreSQL
- Cache: Managed Redis
- APIs: Kubernetes deployments
- Frontend: CDN + Kubernetes
- Ingress: Nginx Ingress Controller
```

## 📈 Scalability Patterns

### Horizontal Scaling
- **Stateless APIs**: Multiple instances behind load balancer
- **Database**: Read replicas for queries
- **Cache**: Redis cluster for high availability

### Micro-Frontend Benefits
- **Independent Deployment**: Each MFE can be deployed separately
- **Team Autonomy**: Different teams can work on different modules
- **Technology Diversity**: Future MFEs can use different frameworks

## 🔍 Monitoring & Observability

### Logging
- **Structured Logging**: Serilog with JSON format
- **Correlation IDs**: Request tracing across services
- **Log Levels**: Debug, Info, Warning, Error

### Health Checks
- **API Health**: /health endpoints
- **Database**: Connection and query tests
- **Dependencies**: External service checks

### Metrics (Future)
- **Application**: Response times, error rates
- **Infrastructure**: CPU, memory, disk usage
- **Business**: Budget creation rates, approval times

## 🔄 Migration Strategy

### Phase 1: Core Budget Management ✅
- Budget creation and management
- User authentication
- Basic reporting

### Phase 2: Advanced Features (Future)
- Workflow automation
- Advanced reporting
- Integration with external systems

### Phase 3: Full Migration (Future)
- All legacy modules migrated
- Advanced analytics
- Mobile applications

## 📚 Development Guidelines

### Code Standards
- **C#**: Microsoft coding conventions
- **TypeScript**: ESLint + Prettier
- **Git**: Conventional commits
- **Testing**: Unit tests for business logic

### API Design
- **RESTful**: Standard HTTP methods and status codes
- **Versioning**: URL versioning (/api/v1/)
- **Documentation**: OpenAPI/Swagger
- **Error Handling**: Consistent error responses

### Frontend Standards
- **Component Design**: Atomic design principles
- **State Management**: React Query for server state
- **Styling**: Ant Design + CSS modules
- **Accessibility**: WCAG 2.1 compliance
