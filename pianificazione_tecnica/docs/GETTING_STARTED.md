# Getting Started - Budget Management System

## 🚀 Quick Start

### Prerequisites

- **Docker & Docker Compose**: Per l'ambiente di sviluppo
- **Node.js 20+**: Per sviluppo frontend
- **.NET 9 SDK**: Per sviluppo backend
- **PostgreSQL**: Database (incluso in Docker Compose)

### 1. Clone e Setup

```bash
# Clone del repository
git clone <repository-url>
cd pianificazione_tecnica

# Copia configurazione
cp config.env.template config.env

# Modifica config.env con le tue impostazioni
nano config.env
```

### 2. Avvio <PERSON>o

```bash
# Rendi eseguibile lo script
chmod +x start-local.sh

# Avvia tutto il sistema
./start-local.sh
```

### 3. Accesso al Sistema

- **Portal**: http://localhost:3000
- **Budget API**: http://localhost:5000/swagger
- **Auth API**: http://localhost:5001/swagger

### 4. Credenziali Demo

```
Email: <EMAIL>
Password: password123
```

## 🛠️ Sviluppo

### Backend Development

```bash
# Naviga nel progetto Budget API
cd budget-api

# Restore dependencies
dotnet restore

# Run migrations
dotnet ef database update -p src/BudgetManagement.Infrastructure -s src/BudgetManagement.WebAPI

# Run API
dotnet run --project src/BudgetManagement.WebAPI
```

### Frontend Development

```bash
# Budget Frontend
cd budget-fe
npm install
npm run dev

# Portal
cd ../portal
npm install
npm run dev
```

## 📁 Struttura Progetto

```
pianificazione_tecnica/
├── 📁 portal/                    # Shell application
├── 📁 auth-api/                  # Authentication service
├── 📁 budget-api/                # Budget management API
├── 📁 budget-fe/                 # Budget micro-frontend
├── 📁 shared/                    # Shared components
├── 📁 docs/                      # Documentation
├── 🐳 docker-compose.yml         # Local development
├── ⚙️ config.env.template        # Configuration template
└── 🚀 start-local.sh             # Startup script
```

## 🔧 Configurazione

### Environment Variables

```bash
# Database
POSTGRES_HOST=localhost
POSTGRES_DB=budget_management
POSTGRES_USER=postgres
POSTGRES_PASSWORD=postgres

# APIs
BUDGET_API_URL=http://localhost:5000
AUTH_API_URL=http://localhost:5001

# Frontend
PORTAL_URL=http://localhost:3000
BUDGET_FE_URL=http://localhost:3001

# Security
JWT_SECRET_KEY=your-secret-key
AZURE_AD_ENABLED=false
```

### Database Setup

```bash
# Create database
createdb budget_management

# Run migrations
cd budget-api
dotnet ef database update
```

## 🧪 Testing

### Backend Tests

```bash
cd budget-api
dotnet test
```

### Frontend Tests

```bash
cd budget-fe
npm test

cd ../portal
npm test
```

## 📊 Monitoring

### Health Checks

```bash
# Budget API
curl http://localhost:5000/api/v1/budget/health

# Auth API
curl http://localhost:5001/api/v1/auth/health

# Frontend
curl http://localhost:3000/health
curl http://localhost:3001/health
```

### Logs

```bash
# View all logs
docker-compose logs -f

# Specific service
docker-compose logs -f budget-api
```

## 🐛 Troubleshooting

### Common Issues

1. **Port già in uso**
   ```bash
   # Trova processo che usa la porta
   lsof -i :3000
   kill -9 <PID>
   ```

2. **Database connection failed**
   ```bash
   # Verifica che PostgreSQL sia in esecuzione
   docker-compose ps postgres
   
   # Restart database
   docker-compose restart postgres
   ```

3. **Module Federation errors**
   ```bash
   # Clear browser cache
   # Restart frontend services
   docker-compose restart budget-fe portal
   ```

### Reset Environment

```bash
# Stop all services
docker-compose down

# Remove volumes (ATTENZIONE: cancella i dati)
docker-compose down -v

# Rebuild everything
docker-compose build --no-cache
./start-local.sh
```

## 📚 Development Workflow

### 1. Feature Development

```bash
# Create feature branch
git checkout -b feature/new-budget-feature

# Make changes
# Test locally
npm test
dotnet test

# Commit changes
git add .
git commit -m "feat: add new budget feature"

# Push and create PR
git push origin feature/new-budget-feature
```

### 2. Database Changes

```bash
# Add migration
cd budget-api
dotnet ef migrations add NewMigration -p src/BudgetManagement.Infrastructure -s src/BudgetManagement.WebAPI

# Update database
dotnet ef database update -p src/BudgetManagement.Infrastructure -s src/BudgetManagement.WebAPI
```

### 3. Frontend Changes

```bash
# Add new component
cd budget-fe/src/components
mkdir NewComponent
touch NewComponent/index.tsx

# Export in module federation
# Update vite.config.ts exposes section
```

## 🔐 Security Guidelines

### API Security

- Sempre usare HTTPS in produzione
- Validare tutti gli input
- Implementare rate limiting
- Usare CORS appropriato

### Frontend Security

- Sanitizzare input utente
- Usare Content Security Policy
- Implementare CSRF protection
- Validare token JWT

## 📈 Performance Tips

### Backend

- Usare async/await
- Implementare caching con Redis
- Ottimizzare query database
- Usare pagination

### Frontend

- Lazy loading dei componenti
- Memoization con React.memo
- Ottimizzare bundle size
- Usare React Query per caching

## 🚀 Deployment

### Development

```bash
# Build all services
docker-compose build

# Deploy to development
docker-compose up -d
```

### Production (Future)

```bash
# Build production images
docker build -t budget-api:prod budget-api/
docker build -t budget-fe:prod budget-fe/

# Deploy to Kubernetes
kubectl apply -f k8s/
```

## 📞 Support

### Documentation

- [Architecture](ARCHITECTURE.md)
- [API Documentation](http://localhost:5000/swagger)
- [Frontend Components](../budget-fe/README.md)

### Team Contacts

- **Backend Team**: <EMAIL>
- **Frontend Team**: <EMAIL>
- **DevOps Team**: <EMAIL>

### Issues

- Create GitHub issues for bugs
- Use discussions for questions
- Check existing documentation first
