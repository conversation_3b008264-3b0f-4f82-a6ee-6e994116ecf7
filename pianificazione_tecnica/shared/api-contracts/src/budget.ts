// Budget API Contracts

export interface Budget {
  id: string;
  plantId: string;
  productionUnitId: string;
  year: number;
  status: BudgetStatus;
  version: number;
  createdBy: string;
  createdAt: string;
  modifiedAt?: string;
  maintenancePlanId?: string;
  approvedAtPlantDate?: string;
  approvedAtPlantBy?: string;
  approvedAtHeadquartersDate?: string;
  approvedAtHeadquartersBy?: string;
  items: BudgetItem[];
}

export interface BudgetItem {
  id: string;
  kpiId: string;
  month: number;
  proposedValue?: number;
  budgetValue?: number;
  notes?: string;
  createdAt: string;
  modifiedAt?: string;
}

export enum BudgetStatus {
  Draft = 'Draft',
  Submitted = 'Submitted',
  ApprovedPlant = 'ApprovedPlant',
  InApprovalHeadquarters = 'InApprovalHeadquarters',
  ApprovedHeadquarters = 'ApprovedHeadquarters',
  Reference = 'Reference',
  Rejected = 'Rejected'
}

export interface CreateBudgetRequest {
  plantId: string;
  productionUnitId: string;
  year: number;
}

export interface CreateBudgetResponse {
  budgetId: string;
  status: string;
  maintenancePlanInfo?: string;
  message?: string;
}

export interface UpdateBudgetRequest {
  status?: BudgetStatus;
  items?: BudgetItemUpdate[];
}

export interface BudgetItemUpdate {
  id?: string;
  kpiId: string;
  month: number;
  proposedValue?: number;
  budgetValue?: number;
  notes?: string;
}

export interface BudgetQuery {
  year?: number;
  plantId?: string;
  status?: BudgetStatus;
  page?: number;
  pageSize?: number;
}

export interface BudgetListResponse {
  items: Budget[];
  totalCount: number;
  page: number;
  pageSize: number;
}

export interface Plant {
  id: string;
  name: string;
  type: PlantType;
  isActive: boolean;
  location?: string;
  capacity?: number;
}

export interface ProductionUnit {
  id: string;
  name: string;
  plantId: string;
  isActive: boolean;
  capacity?: number;
  type?: string;
}

export enum PlantType {
  Thermal = 'Thermal',
  Hydroelectric = 'Hydroelectric'
}

export interface KPI {
  id: string;
  name: string;
  description?: string;
  unit: string;
  category: string;
  isActive: boolean;
}
