using AuthService.Application.Commands.Login;
using AuthService.Application.Commands.RefreshToken;
using AuthService.Application.Queries.GetCurrentUser;
using MediatR;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;

namespace AuthService.WebAPI.Controllers;

/// <summary>
/// Controller for authentication operations
/// </summary>
[ApiController]
[Route("api/v1/[controller]")]
public class AuthController : ControllerBase
{
    private readonly IMediator _mediator;
    private readonly ILogger<AuthController> _logger;

    public AuthController(IMediator mediator, ILogger<AuthController> logger)
    {
        _mediator = mediator;
        _logger = logger;
    }

    /// <summary>
    /// Authenticates a user with email and password
    /// </summary>
    /// <param name="request">Login credentials</param>
    /// <returns>Authentication token and user information</returns>
    [HttpPost("login")]
    [ProducesResponseType(typeof(LoginResponse), StatusCodes.Status200OK)]
    [ProducesResponseType(typeof(ProblemDetails), StatusCodes.Status400BadRequest)]
    [ProducesResponseType(typeof(ProblemDetails), StatusCodes.Status401Unauthorized)]
    public async Task<IActionResult> Login([FromBody] LoginRequest request)
    {
        _logger.LogInformation("Login attempt for user {Email}", request.Email);

        var command = new LoginCommand(request.Email, request.Password);
        var result = await _mediator.Send(command);

        if (result.IsFailure)
        {
            _logger.LogWarning("Login failed for user {Email}: {Error}", request.Email, result.Error);
            return Unauthorized(new ProblemDetails
            {
                Title = "Authentication Failed",
                Detail = result.Error,
                Status = StatusCodes.Status401Unauthorized
            });
        }

        _logger.LogInformation("Login successful for user {Email}", request.Email);
        return Ok(result.Value);
    }

    /// <summary>
    /// Refreshes an authentication token
    /// </summary>
    /// <param name="request">Refresh token request</param>
    /// <returns>New authentication token</returns>
    [HttpPost("refresh")]
    [ProducesResponseType(typeof(RefreshTokenResponse), StatusCodes.Status200OK)]
    [ProducesResponseType(typeof(ProblemDetails), StatusCodes.Status400BadRequest)]
    [ProducesResponseType(typeof(ProblemDetails), StatusCodes.Status401Unauthorized)]
    public async Task<IActionResult> RefreshToken([FromBody] RefreshTokenRequest request)
    {
        var command = new RefreshTokenCommand(request.RefreshToken);
        var result = await _mediator.Send(command);

        if (result.IsFailure)
        {
            return Unauthorized(new ProblemDetails
            {
                Title = "Token Refresh Failed",
                Detail = result.Error,
                Status = StatusCodes.Status401Unauthorized
            });
        }

        return Ok(result.Value);
    }

    /// <summary>
    /// Gets the current authenticated user information
    /// </summary>
    /// <returns>Current user information</returns>
    [HttpGet("me")]
    [Authorize]
    [ProducesResponseType(typeof(UserResponse), StatusCodes.Status200OK)]
    [ProducesResponseType(typeof(ProblemDetails), StatusCodes.Status401Unauthorized)]
    public async Task<IActionResult> GetCurrentUser()
    {
        var query = new GetCurrentUserQuery();
        var result = await _mediator.Send(query);

        if (result.IsFailure)
        {
            return Unauthorized(new ProblemDetails
            {
                Title = "User Not Found",
                Detail = result.Error,
                Status = StatusCodes.Status401Unauthorized
            });
        }

        return Ok(result.Value);
    }

    /// <summary>
    /// Logs out the current user
    /// </summary>
    /// <returns>Success response</returns>
    [HttpPost("logout")]
    [Authorize]
    [ProducesResponseType(StatusCodes.Status200OK)]
    public async Task<IActionResult> Logout()
    {
        // In a real implementation, you might want to blacklist the token
        // or perform other cleanup operations
        _logger.LogInformation("User logged out");
        return Ok(new { message = "Logged out successfully" });
    }

    /// <summary>
    /// Authenticates a user with Azure AD token
    /// </summary>
    /// <param name="request">Azure AD access token</param>
    /// <returns>Authentication token and user information</returns>
    [HttpPost("azure-ad")]
    [ProducesResponseType(typeof(LoginResponse), StatusCodes.Status200OK)]
    [ProducesResponseType(typeof(ProblemDetails), StatusCodes.Status400BadRequest)]
    [ProducesResponseType(typeof(ProblemDetails), StatusCodes.Status401Unauthorized)]
    public async Task<IActionResult> LoginWithAzureAd([FromBody] AzureAdLoginRequest request)
    {
        // TODO: Implement Azure AD authentication
        _logger.LogInformation("Azure AD login attempt");
        
        return BadRequest(new ProblemDetails
        {
            Title = "Not Implemented",
            Detail = "Azure AD authentication is not yet implemented",
            Status = StatusCodes.Status400BadRequest
        });
    }

    /// <summary>
    /// Gets the health status of the auth service
    /// </summary>
    /// <returns>Health status</returns>
    [HttpGet("health")]
    [ProducesResponseType(StatusCodes.Status200OK)]
    public IActionResult GetHealth()
    {
        return Ok(new { Status = "Healthy", Timestamp = DateTime.UtcNow });
    }
}

/// <summary>
/// Login request model
/// </summary>
public record LoginRequest(string Email, string Password);

/// <summary>
/// Login response model
/// </summary>
public record LoginResponse(
    string Token,
    string RefreshToken,
    UserResponse User,
    DateTime ExpiresAt
);

/// <summary>
/// Refresh token request model
/// </summary>
public record RefreshTokenRequest(string RefreshToken);

/// <summary>
/// Refresh token response model
/// </summary>
public record RefreshTokenResponse(
    string Token,
    string RefreshToken,
    UserResponse User,
    DateTime ExpiresAt
);

/// <summary>
/// User response model
/// </summary>
public record UserResponse(
    string Id,
    string Email,
    string Name,
    string[] Roles,
    string[] Permissions
);

/// <summary>
/// Azure AD login request model
/// </summary>
public record AzureAdLoginRequest(string AccessToken);
