# Database Configuration
POSTGRES_HOST=localhost
POSTGRES_PORT=5432
POSTGRES_DB=budget_management
POSTGRES_USER=postgres
POSTGRES_PASSWORD=postgres

# Redis Configuration
REDIS_HOST=localhost
REDIS_PORT=6379

# API Configuration
BUDGET_API_URL=http://localhost:5000
AUTH_API_URL=http://localhost:5001

# Frontend Configuration
PORTAL_URL=http://localhost:3000
BUDGET_FE_URL=http://localhost:3001

# JWT Configuration
JWT_SECRET_KEY=your-super-secret-key-that-is-at-least-32-characters-long
JWT_ISSUER=BudgetManagement
JWT_AUDIENCE=BudgetManagement
JWT_EXPIRATION_MINUTES=60

# Azure AD Configuration (Optional)
AZURE_AD_ENABLED=false
AZURE_AD_TENANT_ID=your-tenant-id
AZURE_AD_CLIENT_ID=your-client-id
AZURE_AD_CLIENT_SECRET=your-client-secret

# CORS Configuration
CORS_ALLOWED_ORIGINS=http://localhost:3000,http://localhost:3001,https://localhost:3000,https://localhost:3001

# Logging Configuration
LOG_LEVEL=Information
ENABLE_SENSITIVE_DATA_LOGGING=false

# Environment
ASPNETCORE_ENVIRONMENT=Development
NODE_ENV=development
