## 🏗️ Core Architecture

### Technology Stack
- **Backend**: .NET 9 APIs with Clean Architecture
- **Frontend**: React 18 + TypeScript + Vite
- **Authentication**: Azure AD / EntraID
- **Database**: PostgreSQL
- **Module Federation**: Webpack Module Federation for micro-frontends
- **Containerization**: Docker with multi-stage builds

### Project Structure Overview

```
project-root/
├── 📁 portal/                           # Shell application (authentication + routing)
├── 📁 auth-api/                         # Authentication service
├── 📁 {service-name}-api/               # Business service APIs
├── 📁 {service-name}-fe/                # Micro-frontend applications
├── 📁 shared/                           # Shared components and utilities
│   ├── 📁 ui-components/               # Reusable React components
│   ├── 📁 api-contracts/               # TypeScript type definitions
│   └── 📁 utilities/                   # Common utilities
├── 📁 infrastructure/                   # Deployment configurations
├── 📄 config.env.template              # Environment configuration template
├── 📄 start-local.sh                   # Development startup script
├── 📄 Dockerfile.frontend              # Generic frontend Dockerfile
├── 📄 nginx.conf                       # Nginx configuration
└── 📄 README.md                        # Project documentation
```

## 🔧 Service Templates

### .NET API Service Structure (Clean Architecture)

```
{service-name}-api/
├── 📁 {ServiceName}.Api/               # Web API layer
│   ├── 📁 Controllers/                 # API controllers
│   ├── 📄 Program.cs                   # Application entry point
│   ├── 📄 appsettings.json            # Configuration
│   └── 📄 {ServiceName}.Api.csproj    # Project file
├── 📁 {ServiceName}.Application/       # Business logic layer
│   ├── 📁 Services/                    # Application services
│   └── 📄 {ServiceName}.Application.csproj
├── 📁 {ServiceName}.Domain/            # Domain layer
│   ├── 📁 Entities/                    # Domain entities
│   ├── 📁 Interfaces/                  # Domain interfaces
│   └── 📄 {ServiceName}.Domain.csproj
├── 📁 {ServiceName}.Infrastructure/    # Infrastructure layer
│   ├── 📁 Data/                        # DbContext and configurations
│   ├── 📁 Repositories/                # Repository implementations
│   └── 📄 {ServiceName}.Infrastructure.csproj
└── 📄 {ServiceName}.sln               # Solution file
```

### React Micro-Frontend Structure

```
{service-name}-fe/
├── 📁 src/
│   ├── 📁 components/                  # React components
│   ├── 📁 hooks/                       # Custom React hooks
│   ├── 📁 types/                       # TypeScript type definitions
│   ├── 📄 App.tsx                      # Main application component
│   ├── 📄 main.tsx                     # Application entry point
│   └── 📄 bootstrap.tsx                # Module Federation bootstrap
├── 📁 public/                          # Static assets
├── 📄 package.json                     # Dependencies and scripts
├── 📄 vite.config.ts                   # Vite + Module Federation config
├── 📄 tsconfig.json                    # TypeScript configuration
├── 📄 Dockerfile                       # Container configuration
└── 📄 README.md                        # Service documentation
```

## 🔐 Authentication Architecture

### Required Azure AD App Registrations

1. **Portal SPA Registration**
   - Application Type: Single-page application
   - Redirect URIs: `http://localhost:3000`, `https://your-domain.com`
   - Used by: Portal frontend for user authentication

2. **Auth API Registration**
   - Application Type: Web API
   - Expose API: Define scopes for business APIs
   - Used by: Auth service for token validation

### Authentication Flow

```mermaid
graph LR
    A[User] --> B[Portal SPA]
    B --> C[Azure AD]
    C --> B
    B --> D[Auth API]
    D --> E[Business APIs]
    B --> F[Micro-frontends]
```

## 📦 Shared Components Architecture

### UI Components Structure

```
shared/ui-components/
├── 📁 DataTable/                       # Reusable data table
│   ├── 📄 DataTable.tsx
│   ├── 📄 DataTable.types.ts
│   └── 📄 index.ts
├── 📁 LoadingSpinner/                  # Loading states
├── 📁 ErrorMessage/                    # Error handling
├── 📁 EmptyState/                      # Empty data states
├── 📁 PageContainer/                   # Page layouts
├── 📁 hooks/                           # Shared React hooks
│   ├── 📄 useApiData.ts               # Data fetching hook
│   └── 📄 index.ts
├── 📄 package.json                     # Component library config
└── 📄 index.ts                         # Main exports
```

### API Contracts Structure

```
shared/api-contracts/
├── 📁 types/                           # TypeScript interfaces
│   ├── 📄 {service-name}.types.ts     # Service-specific types
│   └── 📄 common.types.ts             # Common types
└── 📄 index.ts                         # Type exports
```

## 🚀 Development Workflow

### Environment Configuration

1. **Create environment files from template:**
   ```bash
   cp config.env.template config.env.local
   cp config.env.template config.env.dev
   cp config.env.template config.env.prod
   ```

2. **Required environment variables:**
   ```bash
   # Authentication
   VITE_PORTAL_CLIENT_ID=your-portal-spa-client-id
   VITE_TENANT_ID=your-tenant-id
   AZURE_CLIENT_ID=your-auth-api-client-id
   
   # Database
   PGHOST=your-postgres-host
   PGUSER=your-username
   PGPASSWORD=your-password
   PGDATABASE=your-database
   
   # Service Ports
   PORTAL_PORT=3000
   AUTH_API_PORT=8003
   {SERVICE}_API_PORT=5xxx
   {SERVICE}_FRONTEND_PORT=3xxx
   ```

### Local Development Setup

1. **Install dependencies:**
   ```bash
   # Install frontend dependencies
   cd portal && npm install && cd ..
   cd {service-name}-fe && npm install && cd ..
   
   # Restore .NET dependencies
   cd {service-name}-api && dotnet restore && cd ..
   ```

2. **Start all services:**
   ```bash
   ./start-local.sh
   ```

## 🐳 Containerization

### Frontend Dockerfile Template

```dockerfile
# Multi-stage build for React applications
FROM node:24.3.0-alpine AS build
WORKDIR /app
COPY package*.json ./
RUN npm ci --only=production --silent
COPY . .
ARG VITE_PORTAL_CLIENT_ID
ARG VITE_TENANT_ID
# ... other build args
RUN npm run build

FROM nginx:1.25.3-alpine AS production
COPY --from=build /app/dist /usr/share/nginx/html
COPY nginx.conf /etc/nginx/nginx.conf
EXPOSE 8080
CMD ["nginx", "-g", "daemon off;"]
```

### API Dockerfile Template

```dockerfile
FROM mcr.microsoft.com/dotnet/aspnet:9.0 AS base
WORKDIR /app
EXPOSE 8080

FROM mcr.microsoft.com/dotnet/sdk:9.0 AS build
WORKDIR /src
COPY ["*.csproj", "./"]
RUN dotnet restore
COPY . .
RUN dotnet build -c Release -o /app/build

FROM build AS publish
RUN dotnet publish -c Release -o /app/publish

FROM base AS final
WORKDIR /app
COPY --from=publish /app/publish .
ENTRYPOINT ["dotnet", "{ServiceName}.Api.dll"]
```

## 📋 Adding New Microservices

### Step-by-Step Process

1. **Create API structure:**
   ```bash
   mkdir {service-name}-api
   cd {service-name}-api
   dotnet new sln -n {ServiceName}
   dotnet new webapi -n {ServiceName}.Api
   dotnet new classlib -n {ServiceName}.Application
   dotnet new classlib -n {ServiceName}.Domain
   dotnet new classlib -n {ServiceName}.Infrastructure
   ```

2. **Create frontend structure:**
   ```bash
   mkdir {service-name}-fe
   cd {service-name}-fe
   npm create vite@latest . -- --template react-ts
   npm install @module-federation/vite
   ```

3. **Configure Module Federation:**
   - Update portal's `vite.config.ts` to include new remote
   - Configure new frontend as Module Federation remote
   - Add route in portal's routing configuration

4. **Update startup script:**
   - Add new service ports to `config.env.template`
   - Update `start-local.sh` to include new services
   - Add health checks and monitoring

## 🔍 Key Configuration Files

### Module Federation Configuration (Frontend)

```typescript
// vite.config.ts
import { federation } from "@module-federation/vite";

export default defineConfig({
  plugins: [
    react(),
    federation({
      name: "{serviceName}Fe",
      filename: "remoteEntry.js",
      exposes: {
        "./App": "./src/App.tsx",
      },
      shared: {
        react: { singleton: true },
        "react-dom": { singleton: true },
      },
    }),
  ],
});
```

### API Program.cs Template

```csharp
var builder = WebApplication.CreateBuilder(args);

// Add services
builder.Services.AddControllers();
builder.Services.AddEndpointsApiExplorer();
builder.Services.AddSwaggerGen();

// Add authentication
builder.Services.AddAuthentication("Bearer")
    .AddJwtBearer("Bearer", options => {
        options.Authority = "https://login.microsoftonline.com/{tenant-id}";
        options.Audience = "{api-client-id}";
    });

var app = builder.Build();

// Configure pipeline
if (app.Environment.IsDevelopment())
{
    app.UseSwagger();
    app.UseSwaggerUI();
}

app.UseAuthentication();
app.UseAuthorization();
app.MapControllers();

app.Run();
```

## 📊 Service Ports Convention

| Service Type | Port Range | Example |
|-------------|------------|---------|
| Portal | 3000 | 3000 |
| Frontends | 3001-3099 | 3002, 3005 |
| APIs | 5100-5199 | 5108, 5109 |
| Auth API | 8000-8099 | 8003 |

## 🎯 Best Practices

1. **Consistent naming conventions**
2. **Shared component library usage**
3. **Environment-specific configurations**
4. **Health checks for all services**
5. **Centralized authentication**
6. **Clean Architecture for APIs**
7. **Module Federation for frontends**
8. **Docker multi-stage builds**
9. **Comprehensive documentation**
10. **Automated startup scripts**

This template provides a solid foundation for building scalable microservices platforms that can be easily replicated and extended for different projects.
